"""
小波分析实验脚本
Author: <PERSON><PERSON><PERSON> & Assistant
Date: 2025-07-21
Description: 专注基础小波/迭代小波的方法试验，解耦的方法，测试，可视化
主要特性：
- 解耦的小波分析方法
- 可调节参数界面
- 中间过程可视化
- 灵活的手动调节能力
- 无需复杂配置文件
"""

import numpy as np
import matplotlib.pyplot as plt
import pywt
from scipy import signal
from typing import Dict, List, Tuple, Optional, Any
import os
from scipy.signal import filtfilt, butter, decimate



# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class VMDBaselineDenoiser:
    """VMD基线降噪类"""

    def __init__(self, original_fs=1000, downsample_fs=50, cutoff_freq=0.8):
        """
        初始化VMD基线降噪器

        Args:
            original_fs: 原始采样频率 (Hz)
            downsample_fs: 降采样频率 (Hz)
            cutoff_freq: 低通滤波截止频率 (Hz)
        """
        self.original_fs = original_fs
        self.downsample_fs = downsample_fs
        self.cutoff_freq = cutoff_freq
        self.vmd_params = {
            'alpha': 600,
            'tau': 0,
            'K': 6,
            'DC': 0,
            'init': 1,
            'tol': 1e-3
        }


    def downsample_signal(self, signal):
        """
        使用抗混叠滤波器进行降采样，以避免信号混叠。
        """
        downsample_ratio = self.original_fs // self.downsample_fs

        # 使用scipy.signal.decimate函数，它会自动应用抗混叠滤波器后再进行抽取
        # zero_phase=True 确保了滤波是无相移的，这对于保持信号形态至关重要
        downsampled = decimate(signal, downsample_ratio, ftype='iir', zero_phase=True)

        return downsampled

    def upsample_signal(self, signal, target_length):
        """使用线性插值升采样信号到原始长度，以避免端点震荡"""
        # 创建原始（降采样后）和目标（原始长度）的时间点
        original_points = np.linspace(0, 1, len(signal))
        target_points = np.linspace(0, 1, target_length)

        # 使用线性插值
        upsampled = np.interp(target_points, original_points, signal)
        return upsampled

    def apply_lowpass_filter(self,signal, fs, cutoff):
        """应用无相移低通滤波器，并优化端点效应"""
        # 设计Butterworth低通滤波器
        nyquist = fs / 2
        normalized_cutoff = cutoff / nyquist

        # 确保截止频率不超过奈奎斯特频率
        if normalized_cutoff >= 1.0:
            normalized_cutoff = 0.95

        # 滤波器阶数为4
        order = 4
        b, a = butter(order, normalized_cutoff, btype='low')

        # 使用filtfilt进行零相位滤波，并添加 padlen 参数
        # 一个好的经验法则是 3 * (order), 这里我们用更安全的 3 * (len(b)-1)
        pad_length = 3 * (len(b) - 1)

        # 确保填充长度不会超过信号本身长度的一半
        if pad_length > len(signal) // 2:
            pad_length = len(signal) // 2 - 1

        filtered_signal = filtfilt(b, a, signal, padlen=pad_length)
        return filtered_signal

    def find_lowest_frequency_mode(self, vmd_modes, vmd_omega):
        """找到频率中心最低的VMD分量"""
        # 获取最后一次迭代的频率
        final_omega = vmd_omega[-1, :]

        # 找到最低频率分量的索引
        lowest_freq_idx = np.argmin(final_omega)

        return vmd_modes[lowest_freq_idx, :], lowest_freq_idx, final_omega[lowest_freq_idx]

    def denoise_single_channel(self, signal):
        """
        对单个通道进行VMD基线降噪

        Args:
            signal: 输入信号 (1D numpy array)

        Returns:
            denoised_signal: 降噪后的信号
            baseline: 提取的基线
            vmd_info: VMD分解信息
        """
        try:
            # 导入VMD库
            from vmdpy import VMD
        except ImportError:
            print("警告: 无法导入vmdpy库，请安装: pip install vmdpy")
            # 返回原始信号作为降噪信号，零基线
            return signal, np.zeros_like(signal), {}

        original_length = len(signal)

        # 步骤1: 降采样
        downsampled_signal = self.downsample_signal(signal)

        # 步骤2: VMD分解
        vmd_modes, vmd_u_hat, vmd_omega = VMD(
            downsampled_signal,
            self.vmd_params['alpha'],
            self.vmd_params['tau'],
            self.vmd_params['K'],
            self.vmd_params['DC'],
            self.vmd_params['init'],
            self.vmd_params['tol']
        )

        # 步骤3: 找到最低频率分量
        lowest_freq_mode, mode_idx, center_freq = self.find_lowest_frequency_mode(vmd_modes, vmd_omega)

        # 步骤4: 对最低频率分量进行低通滤波
        filtered_baseline = self.apply_lowpass_filter(
            lowest_freq_mode,
            self.downsample_fs,
            self.cutoff_freq
        )

        # 步骤5: 升采样基线到原始长度
        upsampled_baseline = self.upsample_signal(filtered_baseline, original_length)

        # 步骤6: 原始信号减去基线得到降噪信号
        denoised_signal = signal - upsampled_baseline
        denoised_signal = denoised_signal - np.mean(denoised_signal)
        upsampled_baseline =  signal - denoised_signal


        # 返回结果和相关信息
        vmd_info = {
            'vmd_modes': vmd_modes,
            'vmd_omega': vmd_omega,
            'lowest_freq_mode_idx': mode_idx,
            'center_frequency': center_freq,
            'downsampled_signal': downsampled_signal,
            'filtered_baseline': filtered_baseline
        }

        return denoised_signal, upsampled_baseline, vmd_info


class WaveletAnalyzer:
    """小波分析器 - 解耦设计"""

    def __init__(self, sampling_rate: float = 1000):
        self.sampling_rate = sampling_rate
        self.results = {}

    def load_signal(self, signal: np.ndarray, name: str = "signal") -> str:
        """加载信号"""
        if signal.ndim == 1:
            self.results[name] = {
                'original': signal,
                'name': name,
                'length': len(signal),
                'duration': len(signal) / self.sampling_rate
            }
        elif signal.ndim == 2:
            # 多通道信号，选择第一个通道
            self.results[name] = {
                'original': signal[:, 0],
                'name': name,
                'length': signal.shape[0],
                'duration': signal.shape[0] / self.sampling_rate
            }
        return name

    def wavelet_decompose(self, signal_name: str, wavelet: str = 'db8',
                          levels: int = 6, mode: str = 'symmetric') -> Dict:
        """小波分解"""
        if signal_name not in self.results:
            raise ValueError(f"Signal {signal_name} not found")

        signal = self.results[signal_name]['original']

        # 小波分解
        coeffs = pywt.wavedec(signal, wavelet, level=levels, mode=mode)

        # 估计噪声标准差
        sigma = np.median(np.abs(coeffs[-1])) / 0.6745

        decomp_result = {
            'coeffs': coeffs,
            'wavelet': wavelet,
            'levels': levels,
            'mode': mode,
            'sigma': sigma,
            'signal_energy': np.var(signal),
            'signal_max': np.max(np.abs(signal))
        }

        self.results[signal_name]['decomposition'] = decomp_result

        print(f"小波分解完成:")
        print(f"  小波类型: {wavelet}")
        print(f"  分解层数: {levels}")
        print(f"  噪声估计σ: {sigma:.4f}")
        print(f"  信号最大值: {decomp_result['signal_max']:.4f}")

        return decomp_result

    def threshold_coefficients(self, signal_name: str,
                               threshold_method: str = 'adaptive',
                               threshold_value: float = None,
                               threshold_alpha: float = 0.35,
                               threshold_type: str = 'soft',
                               levels_to_process: List[int] = None) -> Dict:
        """阈值处理系数"""
        if signal_name not in self.results or 'decomposition' not in self.results[signal_name]:
            raise ValueError(f"Need to decompose signal {signal_name} first")

        decomp = self.results[signal_name]['decomposition']
        coeffs = decomp['coeffs']
        signal = self.results[signal_name]['original']

        # 计算阈值
        if threshold_method == 'manual' and threshold_value is not None:
            threshold = threshold_value
        elif threshold_method == 'adaptive':
            # 自适应阈值：基于信号幅值
            signal_max = decomp['signal_max']
            threshold = signal_max * threshold_alpha  # 可调节
        elif threshold_method == 'sigma_based':
            # 基于噪声标准差
            sigma = decomp['sigma']
            threshold = sigma * np.sqrt(2 * np.log(len(signal)))
        elif threshold_method == 'energy_based':
            # 基于信号能量
            signal_energy = decomp['signal_energy']
            threshold = np.sqrt(signal_energy) * threshold_alpha  # 可调节
        else:
            threshold = decomp['signal_max'] * threshold_alpha

        # 决定处理哪些层
        if levels_to_process is None:
            levels_to_process = list(range(1, len(coeffs)))  # 跳过近似系数

        # 应用阈值
        coeffs_thresh = [coeffs[0].copy()]  # 保留近似系数

        for i in range(1, len(coeffs)):
            if i in levels_to_process:
                if threshold_type == 'soft':
                    thresh_coeff = pywt.threshold(coeffs[i], threshold, 'soft')
                elif threshold_type == 'hard':
                    thresh_coeff = pywt.threshold(coeffs[i], threshold, 'hard')
                else:
                    thresh_coeff = coeffs[i].copy()
                coeffs_thresh.append(thresh_coeff)
            else:
                coeffs_thresh.append(coeffs[i].copy())

        threshold_result = {
            'coeffs_thresh': coeffs_thresh,
            'threshold': threshold,
            'threshold_method': threshold_method,
            'threshold_type': threshold_type,
            'levels_processed': levels_to_process,
            'removed_coeffs_ratio': self._calculate_removal_ratio(coeffs, coeffs_thresh)
        }

        self.results[signal_name]['threshold'] = threshold_result

        print(f"阈值处理完成:")
        print(f"  阈值方法: {threshold_method}")
        print(f"  阈值大小: {threshold:.4f}")
        print(f"  阈值类型: {threshold_type}")
        print(f"  处理层级: {levels_to_process}")
        print(f"  系数移除率: {threshold_result['removed_coeffs_ratio']:.2%}")

        return threshold_result

    def reconstruct_signal(self, signal_name: str) -> np.ndarray:
        """重构信号"""
        if signal_name not in self.results or 'threshold' not in self.results[signal_name]:
            raise ValueError(f"Need to threshold signal {signal_name} first")

        threshold_result = self.results[signal_name]['threshold']
        decomp = self.results[signal_name]['decomposition']

        # 重构信号
        coeffs_thresh = threshold_result['coeffs_thresh']
        wavelet = decomp['wavelet']
        mode = decomp['mode']

        reconstructed = pywt.waverec(coeffs_thresh, wavelet, mode=mode)

        # 长度调整
        original_length = len(self.results[signal_name]['original'])
        if len(reconstructed) != original_length:
            reconstructed = reconstructed[:original_length]

        self.results[signal_name]['reconstructed'] = reconstructed

        print(f"信号重构完成，长度: {len(reconstructed)}")

        return reconstructed

    def iterative_denoising(self, signal_name: str, iterations: int = 3,
                            threshold_decay: float = 0.8,
                            residual_weight: float = 0.2) -> np.ndarray:
        """迭代小波降噪"""
        if signal_name not in self.results:
            raise ValueError(f"Signal {signal_name} not found")

        original = self.results[signal_name]['original']
        current_signal = original.copy()

        iteration_history = [original.copy()]

        for i in range(iterations):
            print(f"\n=== 迭代 {i + 1}/{iterations} ===")

            # 创建临时信号名
            temp_name = f"{signal_name}_iter_{i}"
            self.load_signal(current_signal, temp_name)

            # 小波分解
            self.wavelet_decompose(temp_name, wavelet='db8', levels=6)

            # 动态调整阈值
            base_threshold = self.results[temp_name]['decomposition']['signal_max'] * 0.35
            adjusted_threshold = base_threshold * (threshold_decay ** i)

            # 阈值处理
            self.threshold_coefficients(temp_name,
                                        threshold_method='manual',
                                        threshold_value=adjusted_threshold,
                                        threshold_type='soft')

            # 重构
            denoised = self.reconstruct_signal(temp_name)

            # 残差分析
            residual = current_signal - denoised
            residual_energy = np.var(residual)
            signal_energy = np.var(denoised)

            print(f"  阈值: {adjusted_threshold:.4f}")
            print(f"  残差能量比: {residual_energy / signal_energy:.4f}")

            # 智能残差处理
            if residual_energy > 0.05 * signal_energy and i < iterations - 1:
                # 对残差进行小波处理
                self.load_signal(residual, f"{temp_name}_residual")
                self.wavelet_decompose(f"{temp_name}_residual", wavelet='db4', levels=4)
                self.threshold_coefficients(f"{temp_name}_residual",
                                            threshold_method='adaptive',
                                            threshold_type='soft')
                residual_cleaned = self.reconstruct_signal(f"{temp_name}_residual")

                # 加回部分清理后的残差
                weight = min(residual_weight, residual_energy / signal_energy)
                alpha = max(0.05, weight)  # 保证最小权重
                current_signal = denoised + alpha * residual_cleaned
                print(f"  残差权重: {alpha:.3f}")
            else:
                current_signal = denoised
                print(f"  跳过残差处理")

            iteration_history.append(current_signal.copy())

        # 保存迭代历史
        self.results[signal_name]['iterative'] = {
            'final_signal': current_signal,
            'history': iteration_history,
            'iterations': iterations,
            'threshold_decay': threshold_decay,
            'residual_weight': residual_weight
        }

        print(f"\n迭代降噪完成，共{iterations}次迭代")

        return current_signal

    def _calculate_removal_ratio(self, coeffs_orig, coeffs_thresh):
        """计算系数移除率"""
        total_orig = sum(np.sum(np.abs(c)) for c in coeffs_orig[1:])  # 跳过近似系数
        total_thresh = sum(np.sum(np.abs(c)) for c in coeffs_thresh[1:])
        return (total_orig - total_thresh) / total_orig if total_orig > 0 else 0

    # === 可视化方法 ===

    def plot_signal_overview(self, signal_name: str):
        """信号概览可视化"""
        if signal_name not in self.results:
            return

        signal = self.results[signal_name]['original']
        time_axis = np.arange(len(signal)) / self.sampling_rate

        fig, axes = plt.subplots(2, 2, figsize=(15, 8))

        # 时域信号
        axes[0, 0].plot(time_axis, signal, 'b-', linewidth=1)
        axes[0, 0].set_title(f'时域信号 - {signal_name}')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('幅值')
        axes[0, 0].grid(True, alpha=0.3)

        # 信号统计
        axes[0, 1].text(0.1, 0.8, f'长度: {len(signal)}', transform=axes[0, 1].transAxes, fontsize=12)
        axes[0, 1].text(0.1, 0.7, f'持续时间: {len(signal) / self.sampling_rate:.2f}s', transform=axes[0, 1].transAxes,
                        fontsize=12)
        axes[0, 1].text(0.1, 0.6, f'最大值: {np.max(signal):.4f}', transform=axes[0, 1].transAxes, fontsize=12)
        axes[0, 1].text(0.1, 0.5, f'最小值: {np.min(signal):.4f}', transform=axes[0, 1].transAxes, fontsize=12)
        axes[0, 1].text(0.1, 0.4, f'均值: {np.mean(signal):.4f}', transform=axes[0, 1].transAxes, fontsize=12)
        axes[0, 1].text(0.1, 0.3, f'标准差: {np.std(signal):.4f}', transform=axes[0, 1].transAxes, fontsize=12)
        axes[0, 1].set_title('信号统计')
        axes[0, 1].axis('off')

        # 频谱
        freqs = np.fft.fftfreq(len(signal), 1 / self.sampling_rate)
        fft_signal = np.abs(np.fft.fft(signal))

        # 只显示正频率
        pos_mask = freqs >= 0
        axes[1, 0].semilogy(freqs[pos_mask], fft_signal[pos_mask])
        axes[1, 0].set_title('频谱')
        axes[1, 0].set_xlabel('频率 (Hz)')
        axes[1, 0].set_ylabel('幅值')
        axes[1, 0].grid(True, alpha=0.3)

        # 信号直方图
        axes[1, 1].hist(signal, bins=50, alpha=0.7, density=True)
        axes[1, 1].set_title('幅值分布')
        axes[1, 1].set_xlabel('幅值')
        axes[1, 1].set_ylabel('密度')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def plot_wavelet_coefficients(self, signal_name: str, max_display_length: int = 1000):
        """小波系数可视化"""
        if signal_name not in self.results or 'decomposition' not in self.results[signal_name]:
            print(f"请先对信号 {signal_name} 进行小波分解")
            return

        decomp = self.results[signal_name]['decomposition']
        coeffs = decomp['coeffs']
        wavelet = decomp['wavelet']
        sigma = decomp['sigma']

        n_levels = len(coeffs) - 1
        fig, axes = plt.subplots(n_levels + 1, 1, figsize=(15, 3 * (n_levels + 1)))

        if n_levels == 0:
            axes = [axes]

        # 近似系数
        approx = coeffs[0][:max_display_length]
        axes[0].plot(approx, 'b-', linewidth=1)
        axes[0].set_title(f'近似系数 A{n_levels} (σ={sigma:.4f})')
        axes[0].grid(True, alpha=0.3)

        # 细节系数
        for i in range(1, len(coeffs)):
            detail = coeffs[i][:max_display_length]
            axes[i].plot(detail, 'r-', linewidth=1)
            axes[i].set_title(f'细节系数 D{n_levels - i + 1} (长度: {len(coeffs[i])})')
            axes[i].grid(True, alpha=0.3)

            # 添加统计信息
            detail_std = np.std(coeffs[i])
            detail_max = np.max(np.abs(coeffs[i]))
            axes[i].text(0.02, 0.95, f'std: {detail_std:.4f}', transform=axes[i].transAxes,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
            axes[i].text(0.02, 0.85, f'max: {detail_max:.4f}', transform=axes[i].transAxes,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

        axes[-1].set_xlabel('样本')
        plt.suptitle(f'小波系数分解 ({wavelet})')
        plt.tight_layout()
        plt.show()

    def plot_threshold_effects(self, signal_name: str):
        """阈值效果可视化"""
        if signal_name not in self.results or 'threshold' not in self.results[signal_name]:
            print(f"请先对信号 {signal_name} 进行阈值处理")
            return

        decomp = self.results[signal_name]['decomposition']
        threshold_result = self.results[signal_name]['threshold']

        coeffs_orig = decomp['coeffs']
        coeffs_thresh = threshold_result['coeffs_thresh']
        threshold = threshold_result['threshold']

        n_levels = len(coeffs_orig) - 1
        fig, axes = plt.subplots(n_levels, 2, figsize=(15, 3 * n_levels))

        if n_levels == 1:
            axes = axes.reshape(1, -1)

        for i in range(1, len(coeffs_orig)):  # 跳过近似系数
            level_idx = i - 1

            # 原始系数
            axes[level_idx, 0].plot(coeffs_orig[i], 'b-', alpha=0.7, linewidth=1, label='原始')
            axes[level_idx, 0].axhline(y=threshold, color='r', linestyle='--', alpha=0.8, label=f'阈值={threshold:.3f}')
            axes[level_idx, 0].axhline(y=-threshold, color='r', linestyle='--', alpha=0.8)
            axes[level_idx, 0].set_title(f'D{n_levels - level_idx} - 原始系数')
            axes[level_idx, 0].legend()
            axes[level_idx, 0].grid(True, alpha=0.3)

            # 阈值后系数
            axes[level_idx, 1].plot(coeffs_thresh[i], 'r-', linewidth=1, label='阈值后')
            axes[level_idx, 1].set_title(f'D{n_levels - level_idx} - 阈值后系数')
            axes[level_idx, 1].legend()
            axes[level_idx, 1].grid(True, alpha=0.3)

            # 统计信息
            orig_energy = np.sum(coeffs_orig[i] ** 2)
            thresh_energy = np.sum(coeffs_thresh[i] ** 2)
            energy_retention = thresh_energy / orig_energy if orig_energy > 0 else 0

            axes[level_idx, 1].text(0.02, 0.95, f'能量保留: {energy_retention:.2%}',
                                    transform=axes[level_idx, 1].transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))

        plt.suptitle(f'阈值处理效果 ({threshold_result["threshold_method"]}, {threshold_result["threshold_type"]})')
        plt.tight_layout()
        plt.show()

    def plot_reconstruction_comparison(self, signal_name: str):
        """重构结果对比"""
        if signal_name not in self.results or 'reconstructed' not in self.results[signal_name]:
            print(f"请先重构信号 {signal_name}")
            return

        original = self.results[signal_name]['original']
        reconstructed = self.results[signal_name]['reconstructed']
        time_axis = np.arange(len(original)) / self.sampling_rate

        # 计算评估指标
        mse = np.mean((original - reconstructed) ** 2)
        correlation = np.corrcoef(original, reconstructed)[0, 1]
        snr = 10 * np.log10(np.var(reconstructed) / np.var(original - reconstructed)) if np.var(
            original - reconstructed) > 0 else float('inf')

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 信号对比
        axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, linewidth=1, label='原始信号')
        axes[0, 0].plot(time_axis, reconstructed, 'r-', linewidth=1, label='重构信号')
        axes[0, 0].set_title('信号对比')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('幅值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 残差
        residual = original - reconstructed
        axes[0, 1].plot(time_axis, residual, 'g-', linewidth=1)
        axes[0, 1].set_title('残差 (原始 - 重构)')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('幅值')
        axes[0, 1].grid(True, alpha=0.3)

        # 频谱对比
        freqs = np.fft.fftfreq(len(original), 1 / self.sampling_rate)
        fft_orig = np.abs(np.fft.fft(original))
        fft_recon = np.abs(np.fft.fft(reconstructed))

        pos_mask = freqs >= 0
        axes[1, 0].semilogy(freqs[pos_mask], fft_orig[pos_mask], 'b-', alpha=0.7, label='原始')
        axes[1, 0].semilogy(freqs[pos_mask], fft_recon[pos_mask], 'r-', label='重构')
        axes[1, 0].set_title('频谱对比')
        axes[1, 0].set_xlabel('频率 (Hz)')
        axes[1, 0].set_ylabel('幅值')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 评估指标
        axes[1, 1].text(0.1, 0.8, f'MSE: {mse:.6f}', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].text(0.1, 0.7, f'相关系数: {correlation:.4f}', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].text(0.1, 0.6, f'SNR: {snr:.2f} dB', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].text(0.1, 0.5, f'残差均值: {np.mean(residual):.6f}', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].text(0.1, 0.4, f'残差标准差: {np.std(residual):.6f}', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].set_title('评估指标')
        axes[1, 1].axis('off')

        plt.tight_layout()
        plt.show()

        return mse, correlation, snr

    def plot_iterative_evolution(self, signal_name: str):
        """迭代演化过程可视化"""
        if signal_name not in self.results or 'iterative' not in self.results[signal_name]:
            print(f"请先进行信号 {signal_name} 的迭代降噪")
            return

        iterative_result = self.results[signal_name]['iterative']
        history = iterative_result['history']
        iterations = iterative_result['iterations']

        original = history[0]
        time_axis = np.arange(len(original)) / self.sampling_rate

        fig, axes = plt.subplots(2, 3, figsize=(18, 10))

        # 迭代过程
        axes[0, 0].plot(time_axis, original, 'k-', linewidth=2, alpha=0.7, label='原始')
        colors = ['orange', 'green', 'red', 'purple', 'brown']
        for i in range(1, len(history)):
            color = colors[(i - 1) % len(colors)]
            axes[0, 0].plot(time_axis, history[i], color=color, linewidth=1, label=f'迭代 {i}')
        axes[0, 0].set_title('迭代演化过程')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('幅值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 收敛分析
        convergence = []
        for i in range(1, len(history)):
            if i == 1:
                diff = np.sum((history[i] - original) ** 2)
            else:
                diff = np.sum((history[i] - history[i - 1]) ** 2)
            convergence.append(diff)

        axes[0, 1].semilogy(range(1, len(history)), convergence, 'bo-')
        axes[0, 1].set_title('收敛分析')
        axes[0, 1].set_xlabel('迭代次数')
        axes[0, 1].set_ylabel('变化量 (log)')
        axes[0, 1].grid(True, alpha=0.3)

        # SNR演化
        snr_history = []
        for i in range(1, len(history)):
            signal_power = np.var(history[i])
            noise_power = np.var(original - history[i])
            snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else float('inf')
            snr_history.append(snr)

        axes[0, 2].plot(range(1, len(history)), snr_history, 'ro-')
        axes[0, 2].set_title('SNR演化')
        axes[0, 2].set_xlabel('迭代次数')
        axes[0, 2].set_ylabel('SNR (dB)')
        axes[0, 2].grid(True, alpha=0.3)

        # 残差演化
        for i in range(1, min(4, len(history))):
            residual = original - history[i]
            color = colors[(i - 1) % len(colors)]
            axes[1, 0].plot(time_axis[:min(500, len(time_axis))], residual[:min(500, len(residual))],
                            color=color, alpha=0.7, label=f'迭代 {i}')
        axes[1, 0].set_title('残差演化 (前500个样本)')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('残差')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 能量保留
        energy_retention = []
        orig_energy = np.var(original)
        for i in range(1, len(history)):
            retained = np.var(history[i]) / orig_energy
            energy_retention.append(retained)

        axes[1, 1].plot(range(1, len(history)), energy_retention, 'go-')
        axes[1, 1].set_title('能量保留')
        axes[1, 1].set_xlabel('迭代次数')
        axes[1, 1].set_ylabel('能量保留比')
        axes[1, 1].grid(True, alpha=0.3)

        # 最终对比
        final_signal = history[-1]
        axes[1, 2].plot(time_axis, original, 'b-', alpha=0.7, linewidth=1, label='原始')
        axes[1, 2].plot(time_axis, final_signal, 'r-', linewidth=1, label='最终结果')
        axes[1, 2].set_title('最终对比')
        axes[1, 2].set_xlabel('时间 (s)')
        axes[1, 2].set_ylabel('幅值')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    # === 参数调优工具 ===

    def parameter_sweep(self, signal_name: str, parameter: str, values: List[Any],
                        wavelet: str = 'db8', base_levels: int = 6):
        """参数扫描分析"""
        if signal_name not in self.results:
            raise ValueError(f"Signal {signal_name} not found")

        results = []

        for value in values:
            temp_name = f"{signal_name}_sweep_{parameter}_{value}"
            self.load_signal(self.results[signal_name]['original'], temp_name)

            try:
                if parameter == 'threshold_scale':
                    self.wavelet_decompose(temp_name, wavelet=wavelet, levels=base_levels)
                    self.threshold_coefficients(temp_name,
                                                threshold_method='adaptive',
                                                threshold_value=self.results[temp_name]['decomposition'][
                                                                    'signal_max'] * value)
                elif parameter == 'levels':
                    self.wavelet_decompose(temp_name, wavelet=wavelet, levels=int(value))
                    self.threshold_coefficients(temp_name, threshold_method='adaptive')
                elif parameter == 'wavelet':
                    self.wavelet_decompose(temp_name, wavelet=value, levels=base_levels)
                    self.threshold_coefficients(temp_name, threshold_method='adaptive')

                reconstructed = self.reconstruct_signal(temp_name)

                # 计算评估指标
                original = self.results[signal_name]['original']
                mse = np.mean((original - reconstructed) ** 2)
                correlation = np.corrcoef(original, reconstructed)[0, 1]
                snr = 10 * np.log10(np.var(reconstructed) / np.var(original - reconstructed)) if np.var(
                    original - reconstructed) > 0 else float('inf')

                results.append({
                    'value': value,
                    'mse': mse,
                    'correlation': correlation,
                    'snr': snr,
                    'reconstructed': reconstructed
                })

            except Exception as e:
                print(f"参数 {parameter}={value} 处理失败: {e}")
                continue

        # 可视化结果
        if results:
            self._plot_parameter_sweep(parameter, results)

        return results

    def _plot_parameter_sweep(self, parameter: str, results: List[Dict]):
        """参数扫描结果可视化"""
        values = [r['value'] for r in results]
        mses = [r['mse'] for r in results]
        correlations = [r['correlation'] for r in results]
        snrs = [r['snr'] for r in results]

        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # MSE
        axes[0].plot(values, mses, 'bo-')
        axes[0].set_title(f'MSE vs {parameter}')
        axes[0].set_xlabel(parameter)
        axes[0].set_ylabel('MSE')
        axes[0].grid(True, alpha=0.3)

        # 相关系数
        axes[1].plot(values, correlations, 'go-')
        axes[1].set_title(f'相关系数 vs {parameter}')
        axes[1].set_xlabel(parameter)
        axes[1].set_ylabel('相关系数')
        axes[1].grid(True, alpha=0.3)

        # SNR
        axes[2].plot(values, snrs, 'ro-')
        axes[2].set_title(f'SNR vs {parameter}')
        axes[2].set_xlabel(parameter)
        axes[2].set_ylabel('SNR (dB)')
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        # 找出最佳参数
        best_idx = np.argmax(correlations)  # 以相关系数为主要指标
        best_value = values[best_idx]
        print(f"\n最佳 {parameter}: {best_value}")
        print(f"  MSE: {mses[best_idx]:.6f}")
        print(f"  相关系数: {correlations[best_idx]:.4f}")
        print(f"  SNR: {snrs[best_idx]:.2f} dB")


def load_data(file_path):
    """加载数据文件，支持不同分隔符"""
    try:
        try:
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
        return data
    except Exception as e:
        print(e)
        return None




# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# 函数一：陷波滤波器分析 (带残差频谱)
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
def analyze_with_notch_filter(
        input_signal: np.ndarray,
        fs: float,
        f0: float,
        Q: float,
        freq_range: Optional[Tuple[float, float]] = None
):
    """
    使用陷波滤波器进行分析，并可视化原始频谱、滤波器响应和残差频谱。
    """
    print(f"\n--- 开始陷波滤波分析 (f0={f0}, Q={Q}) ---")
    import numpy as np
    from scipy import signal
    import matplotlib.pyplot as plt
    from typing import Optional, Tuple

    # --- 1. 设计并分析滤波器 ---
    b, a = signal.iirnotch(f0, Q, fs)
    w, h_notch = signal.freqz(b, a, fs=fs, worN=8000)

    # --- 2. 应用滤波器 ---
    filtered_signal = signal.filtfilt(b, a, input_signal)

    # --- 3. 计算残差 ---
    residual = input_signal - filtered_signal

    # --- 4. 计算频谱 ---
    N = len(input_signal)
    xf = np.fft.fftfreq(N, 1 / fs)[:N // 2]
    yf_original = 2.0 / N * np.abs(np.fft.fft(input_signal)[:N // 2])
    yf_residual = 2.0 / N * np.abs(np.fft.fft(residual)[:N // 2])

    # --- 5. 可视化 ---
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle(f'陷波滤波器分析: f0={f0} Hz, Q={Q}', fontsize=16)

    # (1) 时域信号对比
    ax1 = axes[0, 0]
    time_vector = np.arange(N) / fs
    ax1.plot(time_vector, input_signal, label='原始信号', alpha=0.7)
    ax1.plot(time_vector, filtered_signal, label='滤波后信号', linewidth=2)
    ax1.set_title("时域信号对比")
    ax1.set_xlabel("时间 (s)")
    ax1.set_ylabel("幅值")
    ax1.legend()
    ax1.grid(True)

    # (2) 时域残差 (被移除的信号)
    ax2 = axes[0, 1]
    ax2.plot(time_vector, residual, color='tab:red')
    ax2.set_title("时域残差 (被移除的信号)")
    ax2.set_xlabel("时间 (s)")
    ax2.set_ylabel("幅值")
    ax2.grid(True)

    # (3) 原始频谱 与 滤波器响应
    ax3 = axes[1, 0]
    ax3.plot(xf, yf_original, label='原始频谱', color='tab:blue')
    ax3.set_title("原始频谱 vs 滤波器增益")
    ax3.set_xlabel("频率 (Hz)")
    ax3.set_ylabel("信号幅值", color='tab:blue')
    ax3.tick_params(axis='y', labelcolor='tab:blue')
    ax3.grid(True, linestyle=':')
    ax3_twin = ax3.twinx()
    ax3_twin.plot(w, np.abs(h_notch), color='tab:green', linewidth=2, label='滤波器增益')
    ax3_twin.set_ylabel("滤波器增益", color='tab:green')
    ax3_twin.tick_params(axis='y', labelcolor='tab:green')
    ax3_twin.set_ylim(-0.05, 1.05)

    # (4) 残差频谱 (关键诊断图)
    ax4 = axes[1, 1]
    ax4.plot(xf, yf_residual, color='tab:red')
    ax4.set_title("残差频谱 (被移除部分的频谱)")
    ax4.set_xlabel("频率 (Hz)")
    ax4.set_ylabel("幅值")
    ax4.grid(True)

    if freq_range:
        for ax in [ax3, ax4]:
            ax.set_xlim(freq_range)

    fig.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()
    return filtered_signal, residual


# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# 函数二：带阻滤波器分析 (完整可视化)
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
def analyze_with_bandstop_filter(
        input_signal: np.ndarray,
        fs: float,
        lowcut: float,
        highcut: float,
        order: int,
        freq_range: Optional[Tuple[float, float]] = None
):
    """
    使用巴特沃斯带阻滤波器进行分析，并提供完整的可视化。
    """
    print(f"\n--- 开始带阻滤波分析 (范围=[{lowcut}, {highcut}] Hz, 阶数={order}) ---")
    import numpy as np
    from scipy import signal
    import matplotlib.pyplot as plt
    from typing import Optional, Tuple

    # --- 1. 设计并分析滤波器 ---
    nyq = 0.5 * fs
    low = lowcut / nyq
    high = highcut / nyq
    sos = signal.butter(order, [low, high], btype='bandstop', output='sos')
    w, h_bandstop = signal.sosfreqz(sos, fs=fs, worN=8000)

    # --- 2. 应用滤波器 ---
    filtered_signal = signal.sosfiltfilt(sos, input_signal)

    # --- 3. 计算频谱 ---
    N = len(input_signal)
    xf = np.fft.fftfreq(N, 1 / fs)[:N // 2]
    yf_original = 2.0 / N * np.abs(np.fft.fft(input_signal)[:N // 2])
    yf_filtered = 2.0 / N * np.abs(np.fft.fft(filtered_signal)[:N // 2])

    # --- 4. 可视化 ---
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle(f'带阻滤波器分析: 范围=[{lowcut}, {highcut}] Hz, 阶数={order}', fontsize=16)

    # (1) 时域信号对比
    ax1 = axes[0, 0]
    time_vector = np.arange(N) / fs
    ax1.plot(time_vector, input_signal, label='原始信号', alpha=0.7)
    ax1.plot(time_vector, filtered_signal, label='滤波后信号', linewidth=2)
    ax1.set_title("时域信号对比")
    ax1.set_xlabel("时间 (s)")
    ax1.set_ylabel("幅值")
    ax1.legend()
    ax1.grid(True)

    # (2) 原始频谱 vs 滤波器响应
    ax2 = axes[1, 0]
    ax2.plot(xf, yf_original, label='原始频谱', color='tab:blue')
    ax2.set_title("原始频谱 vs 滤波器增益")
    ax2.set_xlabel("频率 (Hz)")
    ax2.set_ylabel("信号幅值", color='tab:blue')
    ax2.tick_params(axis='y', labelcolor='tab:blue')
    ax2.grid(True, linestyle=':')
    ax2_twin = ax2.twinx()
    ax2_twin.plot(w, np.abs(h_bandstop), color='tab:green', linewidth=2, label='滤波器增益')
    ax2_twin.set_ylabel("滤波器增益", color='tab:green')
    ax2_twin.tick_params(axis='y', labelcolor='tab:green')
    ax2_twin.set_ylim(-0.05, 1.05)

    # (3) 滤波后频谱
    ax3 = axes[1, 1]
    ax3.plot(xf, yf_filtered, color='tab:orange')
    ax3.set_title("滤波后频谱")
    ax3.set_xlabel("频率 (Hz)")
    ax3.set_ylabel("幅值")
    ax3.grid(True)

    # 为了对比，也在时域残差图上看看
    ax4 = axes[0, 1]
    residual = input_signal - filtered_signal
    ax4.plot(time_vector, residual, color='tab:red')
    ax4.set_title("时域残差 (被移除的信号)")
    ax4.set_xlabel("时间 (s)")
    ax4.set_ylabel("幅值")
    ax4.grid(True)

    if freq_range:
        for ax in [ax2, ax3]:
            ax.set_xlim(freq_range)

    fig.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()
    return filtered_signal, residual


def apply_multi_notch_filter(
        signal_1d: np.ndarray,
        fs: float,
        notch_freqs: list,
        Q: float = 30.0
):
    """
    对单通道信号应用多个陷波滤波器。

    参数:
    signal_1d (np.ndarray): 输入的单通道信号。
    fs (float): 采样频率 (Hz)。
    notch_freqs (list): 需要滤除的频率列表, e.g., [20, 40, 60]。
    Q (float): 应用于所有陷波器的品质因数。Q值越大，陷波越窄。
    """
    import numpy as np
    import matplotlib.pyplot as plt
    from scipy import signal
    print(f"\n--- 开始多点陷波滤波，目标频率: {notch_freqs} Hz ---")

    # 从原始信号开始，迭代应用每一个陷波滤波器
    filtered_signal = signal_1d.copy()
    for f0 in notch_freqs:
        # 设计IIR陷波滤波器
        b, a = signal.iirnotch(f0, Q, fs)
        # 应用零相位滤波器
        filtered_signal = signal.filtfilt(b, a, filtered_signal)

    # --- 可视化 ---
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    N = len(signal_1d)
    xf = np.fft.fftfreq(N, 1 / fs)[:N // 2]
    yf_original = 2.0 / N * np.abs(np.fft.fft(signal_1d)[:N // 2])
    yf_filtered = 2.0 / N * np.abs(np.fft.fft(filtered_signal)[:N // 2])

    fig, axes = plt.subplots(2, 1, figsize=(12, 8), sharex=False)
    fig.suptitle('多点陷波滤波结果', fontsize=16)

    # 时域对比
    time_vector = np.arange(N) / fs
    axes[0].plot(time_vector, signal_1d, label='原始信号', alpha=0.7)
    axes[0].plot(time_vector, filtered_signal, label='滤波后信号', linewidth=2)
    axes[0].set_title('时域对比')
    axes[0].set_xlabel('时间 (s)')
    axes[0].set_ylabel('幅值')
    axes[0].legend()
    axes[0].grid(True)

    # 频谱对比
    axes[1].plot(xf, yf_original, label='原始频谱', alpha=0.7)
    axes[1].plot(xf, yf_filtered, label='滤波后频谱', linewidth=2)
    axes[1].set_title('频谱对比')
    axes[1].set_xlabel('频率 (Hz)')
    axes[1].set_ylabel('幅值')
    for f0 in notch_freqs:
        axes[1].axvline(x=f0, color='r', linestyle='--', alpha=0.8,
                        label=f'目标 {f0} Hz' if f0 == notch_freqs[0] else "")
    axes[1].legend()
    axes[1].grid(True)
    axes[1].set_xlim(0, max(notch_freqs) * 1.5)  # 自动调整显示范围

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()



from sklearn.decomposition import PCA, FastICA
from scipy.linalg import eigh, sqrtm, inv
from scipy.signal import stft, istft, hilbert
import pywt
from scipy import signal
from scipy.signal import find_peaks
from sklearn.decomposition import FastICA

class SOBI:
    """
    SOBI (Second Order Blind Identification) 算法实现
    基于二阶统计量的盲源分离方法
    """

    def __init__(self, n_components=None, tau_max=10, n_tau=5, random_state=None):
        """
        参数:
        n_components (int): 提取的成分数量，默认为输入数据的通道数
        tau_max (int): 最大时延，默认为10
        n_tau (int): 使用的时延数量，默认为5
        random_state (int): 随机种子
        """
        self.n_components = n_components
        self.tau_max = tau_max
        self.n_tau = n_tau
        self.random_state = random_state
        self.mixing_matrix_ = None
        self.unmixing_matrix_ = None
        self.whitening_matrix_ = None
        self.mean_ = None

    def _whiten_data(self, X):
        """数据白化"""
        # 中心化
        self.mean_ = np.mean(X, axis=0)
        X_centered = X - self.mean_

        # 计算协方差矩阵
        cov_matrix = np.cov(X_centered.T)

        # 特征值分解
        eigenvals, eigenvecs = eigh(cov_matrix)

        # 按特征值降序排列
        idx = np.argsort(eigenvals)[::-1]
        eigenvals = eigenvals[idx]
        eigenvecs = eigenvecs[:, idx]

        # 白化矩阵
        self.whitening_matrix_ = np.dot(eigenvecs, np.diag(1.0 / np.sqrt(eigenvals)))

        # 白化数据
        X_whitened = np.dot(X_centered, self.whitening_matrix_)

        return X_whitened

    def _compute_covariance_matrices(self, X_whitened):
        """计算不同时延下的协方差矩阵"""
        n_samples, n_features = X_whitened.shape

        # 选择时延值
        if self.n_tau == 1:
            taus = [0]
        else:
            taus = np.linspace(0, self.tau_max, self.n_tau, dtype=int)

        covariance_matrices = []

        for tau in taus:
            if tau == 0:
                # tau=0时就是单位矩阵（因为数据已白化）
                cov_tau = np.eye(n_features)
            else:
                # 计算时延协方差矩阵
                if tau < n_samples:
                    X1 = X_whitened[:-tau, :]
                    X2 = X_whitened[tau:, :]
                    cov_tau = np.dot(X1.T, X2) / (n_samples - tau)
                else:
                    cov_tau = np.zeros((n_features, n_features))

            covariance_matrices.append(cov_tau)

        return covariance_matrices

    def _joint_diagonalization(self, matrices):
        """联合对角化多个矩阵"""
        n_matrices = len(matrices)
        n_features = matrices[0].shape[0]

        # 初始化联合对角化矩阵
        if self.random_state is not None:
            np.random.seed(self.random_state)

        # 使用第一个矩阵的特征向量作为初始化
        _, V = eigh(matrices[0])

        # 迭代优化
        max_iter = 100
        for iter_num in range(max_iter):
            # 计算所有矩阵在当前V下的对角化程度
            total_off_diag = 0

            for i in range(n_features):
                for j in range(i + 1, n_features):
                    # 计算Givens旋转角度
                    g11 = g12 = g21 = g22 = 0

                    for matrix in matrices:
                        transformed = np.dot(V.T, np.dot(matrix, V))
                        g11 += transformed[i, i] - transformed[j, j]
                        g12 += transformed[i, j] + transformed[j, i]
                        g21 += transformed[i, j] - transformed[j, i]
                        g22 += transformed[i, i] + transformed[j, j]

                    # 计算旋转角度
                    if abs(g11) > 1e-12 or abs(g12) > 1e-12:
                        theta = 0.25 * np.arctan2(g12, g11)

                        # 应用Givens旋转
                        c = np.cos(theta)
                        s = np.sin(theta)

                        G = np.eye(n_features)
                        G[i, i] = c
                        G[j, j] = c
                        G[i, j] = s
                        G[j, i] = -s

                        V = np.dot(V, G)

                        total_off_diag += abs(g12)

            # 检查收敛
            if total_off_diag < 1e-12:
                break

        return V

    def fit(self, X):
        """训练SOBI模型"""
        n_samples, n_features = X.shape

        if self.n_components is None:
            self.n_components = n_features

        # 白化数据
        X_whitened = self._whiten_data(X)

        # 计算不同时延的协方差矩阵
        covariance_matrices = self._compute_covariance_matrices(X_whitened)

        # 联合对角化
        self.unmixing_matrix_ = self._joint_diagonalization(covariance_matrices)

        # 计算混合矩阵
        whitening_unmixing = np.dot(self.whitening_matrix_, self.unmixing_matrix_)
        self.mixing_matrix_ = inv(whitening_unmixing)

        return self

    def transform(self, X):
        """应用SOBI变换得到源信号"""
        if self.unmixing_matrix_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        # 中心化
        X_centered = X - self.mean_

        # 白化
        X_whitened = np.dot(X_centered, self.whitening_matrix_)

        # 应用解混矩阵
        sources = np.dot(X_whitened, self.unmixing_matrix_)

        return sources

    def fit_transform(self, X):
        """训练并变换"""
        return self.fit(X).transform(X)

    def inverse_transform(self, sources):
        """从源信号重构原始信号"""
        if self.mixing_matrix_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        # 应用混合矩阵
        reconstructed = np.dot(sources, self.mixing_matrix_.T)

        # 加回均值
        reconstructed += self.mean_

        return reconstructed
class ConvolutiveBSS:
    """
    卷积盲源分离 (Convolutive Blind Source Separation, CBSS)
    基于频域独立向量分析 (IVA) 的实现
    """

    def __init__(self, n_components=None, n_fft=512, hop_length=None,
                 max_iter=100, tol=1e-6, random_state=None):
        self.n_components = n_components
        self.n_fft = n_fft
        self.hop_length = hop_length if hop_length is not None else n_fft // 4
        self.max_iter = max_iter
        self.tol = tol
        self.random_state = random_state
        self.unmixing_filters_ = None
        self.n_frequencies_ = None
        self.n_frames_ = None

    def _initialize_unmixing_filters(self, n_channels, n_frequencies):
        """初始化解混滤波器"""
        if self.random_state is not None:
            np.random.seed(self.random_state)

        W = np.zeros((n_frequencies, n_channels, n_channels), dtype=complex)
        for f in range(n_frequencies):
            W[f] = np.eye(n_channels, dtype=complex) + 0.01 * (
                    np.random.randn(n_channels, n_channels) + 1j * np.random.randn(n_channels, n_channels)
            )
        return W

    def _natural_gradient_iva(self, X):
        """使用自然梯度的独立向量分析 (IVA)"""
        n_frequencies, n_channels, n_frames = X.shape
        W = self._initialize_unmixing_filters(n_channels, n_frequencies)

        for iteration in range(self.max_iter):
            W_old = W.copy()

            for f in range(n_frequencies):
                X_f = X[f]  # (n_channels, n_frames)
                Y_f = np.dot(W[f], X_f)  # (n_channels, n_frames)

                # 计算源的幅度
                source_norms = np.abs(Y_f) + 1e-12
                phi = 1.0 / source_norms  # 权重函数

                # 构建期望矩阵
                E_phi_yy = np.zeros((n_channels, n_channels), dtype=complex)
                for i in range(n_channels):
                    for j in range(n_channels):
                        E_phi_yy[i, j] = np.mean(phi[i] * np.conj(Y_f[j]) * Y_f[i])

                # 自然梯度更新
                I = np.eye(n_channels, dtype=complex)
                gradient = I - E_phi_yy
                learning_rate = 0.1
                W[f] = W[f] + learning_rate * np.dot(gradient, W[f])

            # 检查收敛性
            diff = np.mean(np.abs(W - W_old))
            if diff < self.tol:
                print(f"CBSS在第 {iteration + 1} 次迭代后收敛")
                break

        return W

    def fit(self, X):
        """训练CBSS模型"""
        n_samples, n_channels = X.shape
        if self.n_components is None:
            self.n_components = n_channels

        # 对每个通道进行STFT
        X_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(X[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            X_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = X_stft_list[0].shape[0]
        X_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            X_stft[ch] = X_stft_list[ch][:, :min_frames]

        self.n_frequencies_, self.n_frames_ = n_frequencies, min_frames

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        X_freq = np.transpose(X_stft, (1, 0, 2))

        # 应用IVA算法
        self.unmixing_filters_ = self._natural_gradient_iva(X_freq)

        return self

    def transform(self, X):
        """应用CBSS变换得到源信号"""
        if self.unmixing_filters_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        n_samples, n_channels = X.shape

        # STFT变换
        X_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(X[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            X_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = X_stft_list[0].shape[0]
        X_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            X_stft[ch] = X_stft_list[ch][:, :min_frames]

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        X_freq = np.transpose(X_stft, (1, 0, 2))
        n_frequencies, n_channels_used, n_frames = X_freq.shape

        # 应用解混滤波器
        Y_freq = np.zeros_like(X_freq)
        for f in range(n_frequencies):
            Y_freq[f] = np.dot(self.unmixing_filters_[f], X_freq[f])

        # 转换回时域
        sources = np.zeros((n_samples, n_channels))
        for ch in range(n_channels):
            Y_ch = Y_freq[:, ch, :]
            t, y = istft(Y_ch, nperseg=self.n_fft,
                         noverlap=self.n_fft - self.hop_length)

            # 确保长度匹配
            min_len = min(len(y), n_samples)
            sources[:min_len, ch] = y[:min_len]

        return sources

    def fit_transform(self, X):
        """训练并变换"""
        return self.fit(X).transform(X)

    def inverse_transform(self, sources):
        """从源信号重构原始信号"""
        if self.unmixing_filters_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        n_samples, n_channels = sources.shape

        # STFT变换源信号
        S_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(sources[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            S_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = S_stft_list[0].shape[0]
        S_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            S_stft[ch] = S_stft_list[ch][:, :min_frames]

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        S_freq = np.transpose(S_stft, (1, 0, 2))
        n_frequencies, n_channels_used, n_frames = S_freq.shape

        # 计算混合滤波器（解混滤波器的逆）
        X_freq = np.zeros_like(S_freq)
        for f in range(n_frequencies):
            try:
                # 使用训练时保存的解混滤波器进行逆变换
                mixing_filter = np.linalg.inv(self.unmixing_filters_[f])
                X_freq[f] = np.dot(mixing_filter, S_freq[f])
            except np.linalg.LinAlgError:
                # 如果矩阵奇异，使用伪逆
                mixing_filter = np.linalg.pinv(self.unmixing_filters_[f])
                X_freq[f] = np.dot(mixing_filter, S_freq[f])

        # 转换回时域
        reconstructed = np.zeros((n_samples, n_channels))
        for ch in range(n_channels):
            X_ch = X_freq[:, ch, :]
            t, x = istft(X_ch, nperseg=self.n_fft,
                         noverlap=self.n_fft - self.hop_length)

            # 确保长度匹配
            min_len = min(len(x), n_samples)
            reconstructed[:min_len, ch] = x[:min_len]

        return reconstructed

class AdvancedDenoisingStrategy:
    """
    高级降噪策略类 (V9版 - 重构源分离逻辑)

    主要改进：
    - 预处理: 改进的小波基线滤波
    - 步骤一: 可选 'notch', 'wavelet', 或 'notch_then_wavelet'
    - 步骤二: 多源ICA分离与回收，使用稳定的参考模板
    - 步骤三: 合成最终信号
    - 可视化: 针对多源回收的增强可视化

    新的源分离逻辑：
    1. 使用PTP前几名通道的均值作为参考模板
    2. 选择前x名相似度高的源进行回收
    3. 每个通道的回收信号保持独立性
    """

    def __init__(self, fs: float):
        """初始化策略的配置参数"""
        self.fs = fs
        # 内部状态变量
        self.original_signal_ = None
        self.preprocessed_signal_ = None
        self.base_signal_ = None
        self.residual_ = None
        self.final_signal_ = None
        self.wavelet_coeffs_ = None

        # 新的ICA相关状态
        self.ica_model_ = None
        self.ica_sources_ = None
        self.recovered_signal_ = None
        self.reference_template_ = None
        self.source_scores_ = None
        self.selected_sources_idx_ = []
        self.recovery_applied_ = False
        self.reference_channels_ = []

    def _validate_wavelet_params(self, signal_length, wavelet, level):
        """验证小波参数的有效性"""
        try:
            if wavelet not in pywt.wavelist():
                print(f"警告: 小波类型 '{wavelet}' 无效，使用默认的 'db4'")
                wavelet = 'db4'

            max_level = pywt.dwt_max_level(signal_length, wavelet)
            if level > max_level:
                print(f"警告: 分解级数 {level} 超过最大值 {max_level}，自动调整为 {max_level}")
                level = max_level

            return wavelet, level
        except Exception as e:
            print(f"小波参数验证失败: {e}，使用安全默认值")
            return 'db4', min(6, pywt.dwt_max_level(signal_length, 'db4'))

    def _preprocess_baseline_filter(self, signal_nd, config: dict):
        """
        通用预处理步骤：根据配置选择方法移除基线。

        支持的方法:
        - 'wavelet': (原始方法) 使用小波分解和阈值化。
        - 'vmd': 使用VMD基线降噪方法。
        - 'lowpass': 使用低通滤波器提取基线，然后从原信号中减去。
        - 'highpass': 直接使用高通滤波器滤除基线。
        - 'none' or missing: 不执行任何操作。
        """
        from scipy import signal
        method = config.get('method', 'none')
        print(f"--- 预处理: 应用基线滤波 (方法: {method}) ---")

        if method == 'none':
            return signal_nd

        elif method == 'wavelet':
            # 这是您原来的小波基线滤波逻辑
            wavelet = config.get('wavelet_type', 'sym8')
            level = config.get('wavelet_level', 8)
            threshold = config.get('threshold', 6.0)
            print(f"    参数: wavelet={wavelet}, level={level}, threshold={threshold}")
            # 注意：为了代码简洁，这里直接调用了您原来的函数。
            # 实际应用中，您可以将 _preprocess_wavelet_baseline 的内部逻辑直接移到这里。
            return self._preprocess_wavelet_baseline(signal_nd, wavelet, level, threshold)

        elif method == 'vmd':
            # VMD基线降噪方法
            original_fs = config.get('original_fs', self.fs)
            downsample_fs = config.get('downsample_fs', 50)
            cutoff_freq = config.get('cutoff_freq', 0.8)
            print(f"    参数: original_fs={original_fs}Hz, downsample_fs={downsample_fs}Hz, cutoff_freq={cutoff_freq}Hz")

            # 创建VMD降噪器
            vmd_denoiser = VMDBaselineDenoiser(
                original_fs=original_fs,
                downsample_fs=downsample_fs,
                cutoff_freq=cutoff_freq
            )

            # 处理每个通道
            denoised_signal_nd = np.zeros_like(signal_nd)
            n_samples, n_channels = signal_nd.shape

            print(f"    正在处理 {n_channels} 个通道...")
            for i in range(n_channels):
                try:
                    # 对单个通道进行VMD基线降噪
                    denoised_signal, baseline, vmd_info = vmd_denoiser.denoise_single_channel(signal_nd[:, i])
                    denoised_signal_nd[:, i] = denoised_signal

                    if i == 0:  # 只为第一个通道打印详细信息
                        print(f"    通道 {i}: VMD分解完成，最低频模态索引={vmd_info.get('lowest_freq_mode_idx', 'N/A')}")

                except Exception as e:
                    print(f"    警告: 通道 {i} VMD处理失败: {e}，使用原始信号")
                    denoised_signal_nd[:, i] = signal_nd[:, i]

            return denoised_signal_nd

        elif method in ['lowpass', 'highpass']:
            cutoff = config.get('cutoff_freq', 0.5)  # 截止频率，例如0.5Hz
            order = config.get('order', 4)  # 滤波器阶数
            print(f"    参数: cutoff_freq={cutoff}Hz, order={order}")

            # 设计滤波器
            nyquist = 0.5 * self.fs
            if cutoff >= nyquist:
                print(f"    警告: 截止频率 {cutoff}Hz 高于或等于奈奎斯特频率 {nyquist}Hz。跳过滤波。")
                return signal_nd

            norm_cutoff = cutoff / nyquist

            if method == 'lowpass':
                # 设计低通滤波器提取基线
                b, a = signal.butter(order, norm_cutoff, btype='low', analog=False)
                baseline = signal.filtfilt(b, a, signal_nd, axis=0)
                return signal_nd - baseline
            else:  # highpass
                # 设计高通滤波器直接移除基线
                b, a = signal.butter(order, norm_cutoff, btype='high', analog=False)
                return signal.filtfilt(b, a, signal_nd, axis=0)

        else:
            print(f"    警告: 未知的基线滤波方法 '{method}'。跳过此步骤。")
            return signal_nd
    def _preprocess_wavelet_baseline(self, signal_nd, wavelet, level, threshold):
        """预处理：改进的小波阈值方法移除基线"""
        print(f"--- 预处理: 正在应用小波基线滤波 (threshold={threshold})...")

        wavelet, level = self._validate_wavelet_params(signal_nd.shape[0], wavelet, level)

        if self.wavelet_coeffs_ is None:
            print("    执行一次性小波分解...")
            self.wavelet_coeffs_ = []
            for i in range(signal_nd.shape[1]):
                try:
                    coeffs = pywt.wavedec(signal_nd[:, i], wavelet, level=level)
                    self.wavelet_coeffs_.append(coeffs)
                except Exception as e:
                    print(f"    通道 {i} 分解失败: {e}")
                    dummy_coeffs = [signal_nd[:, i]] + [np.zeros(10)] * level
                    self.wavelet_coeffs_.append(dummy_coeffs)

        baseline_removed_signal = np.zeros_like(signal_nd)
        for i in range(signal_nd.shape[1]):
            try:
                coeffs = self.wavelet_coeffs_[i]
                thresholded_coeffs = [coeffs[0]]

                for detail_coeff in coeffs[1:]:
                    if len(detail_coeff) > 0:
                        coeff_std = np.std(detail_coeff)
                        adaptive_threshold = min(threshold, threshold * coeff_std / (coeff_std + 1e-6))
                        thresholded_coeff = pywt.threshold(detail_coeff,
                                                           value=adaptive_threshold,
                                                           mode='hard')
                    else:
                        thresholded_coeff = detail_coeff
                    thresholded_coeffs.append(thresholded_coeff)

                reconstructed_ch = pywt.waverec(thresholded_coeffs, wavelet)
                if len(reconstructed_ch) != signal_nd.shape[0]:
                    reconstructed_ch = reconstructed_ch[:signal_nd.shape[0]]
                baseline_removed_signal[:, i] = reconstructed_ch

            except Exception as e:
                print(f"    通道 {i} 基线移除失败: {e}")
                baseline_removed_signal[:, i] = signal_nd[:, i]

        return baseline_removed_signal

    def _step1_notch_filter(self, signal_nd, top_n_peaks, freq_cutoff, Q):
        """步骤一：陷波滤波"""
        print(f"--- 步骤 1: 陷波滤波 (top_n={top_n_peaks}, Q={Q}) ---")
        base_signal = np.zeros_like(signal_nd)
        n_samples, n_channels = signal_nd.shape

        for i in range(n_channels):
            ch_data = signal_nd[:, i]
            N = n_samples
            xf = np.fft.fftfreq(N, 1 / self.fs)
            yf = np.abs(np.fft.fft(ch_data))
            mask = (xf > freq_cutoff) & (xf < self.fs / 2)

            if np.sum(mask) == 0:
                base_signal[:, i] = ch_data
                continue

            peaks, properties = find_peaks(yf[mask], height=0)
            if len(peaks) == 0:
                base_signal[:, i] = ch_data
                continue

            top_peak_indices = peaks[np.argsort(properties['peak_heights'])[::-1][:top_n_peaks]]
            freqs_to_notch = xf[mask][top_peak_indices]
            filtered_ch = ch_data.copy()

            for f0 in freqs_to_notch:
                if f0 > 0:
                    try:
                        b, a = signal.iirnotch(f0, Q, self.fs)
                        filtered_ch = signal.filtfilt(b, a, filtered_ch)
                    except Exception as e:
                        print(f"    通道 {i} 频率 {f0:.1f}Hz 陷波失败: {e}")

            base_signal[:, i] = filtered_ch
        return base_signal

    def _step1_wavelet_threshold(self, signal_nd, wavelet, level, threshold):
        """步骤一：小波阈值降噪"""
        print(f"--- 步骤 1: 小波阈值降噪 (wavelet={wavelet}, level={level}, threshold={threshold}) ---")

        wavelet, level = self._validate_wavelet_params(signal_nd.shape[0], wavelet, level)

        if self.wavelet_coeffs_ is None:
            print("    执行一次性小波分解...")
            self.wavelet_coeffs_ = []
            for i in range(signal_nd.shape[1]):
                try:
                    coeffs = pywt.wavedec(signal_nd[:, i], wavelet, level=level)
                    self.wavelet_coeffs_.append(coeffs)
                except Exception as e:
                    print(f"    通道 {i} 分解失败: {e}")
                    dummy_coeffs = [signal_nd[:, i]] + [np.zeros(10)] * level
                    self.wavelet_coeffs_.append(dummy_coeffs)

        base_signal = np.zeros_like(signal_nd)
        for i in range(signal_nd.shape[1]):
            try:
                coeffs = self.wavelet_coeffs_[i]
                thresholded_coeffs = [coeffs[0]]
                thresholded_coeffs.extend([pywt.threshold(c, value=threshold, mode='hard') for c in coeffs[1:]])
                reconstructed_ch = pywt.waverec(thresholded_coeffs, wavelet)
                base_signal[:, i] = reconstructed_ch[:len(signal_nd)]
            except Exception as e:
                print(f"    通道 {i} 重构失败: {e}")
                base_signal[:, i] = signal_nd[:, i]

        return base_signal

    def _create_reference_template(self, top_n_channels=3, use_median=True, denoise_template=True):
        """
        创建更鲁棒的参考模板。

        改进：
        1. 可选使用中位数(median)替代平均值(mean)来抵抗异常值。
        2. 可选对生成后的模板进行简单的平滑降噪。
        """
        print(f"\n--- 创建参考模板 (使用PTP前{top_n_channels}名通道) ---")

        # 计算各通道的PTP值
        ptp_values = np.ptp(self.base_signal_, axis=0)
        n_channels = len(ptp_values)

        # 确保top_n_channels不超过实际通道数
        num_to_select = min(top_n_channels, n_channels)
        if num_to_select == 0:
            print("    警告: 没有可用的基础信号通道来创建模板。")
            self.reference_template_ = np.zeros(self.base_signal_.shape[0])
            self.reference_channels_ = []
            return self.reference_template_

        # 选择PTP前几名的通道
        top_channels_idx = np.argsort(ptp_values)[::-1][:num_to_select]
        self.reference_channels_ = top_channels_idx.tolist()

        print(f"    选择的参考通道: {self.reference_channels_}")
        print(f"    对应PTP值: {[f'{ptp_values[i]:.2f}' for i in self.reference_channels_]}")

        # 获取用于创建模板的信号
        reference_signals = self.base_signal_[:, self.reference_channels_]

        # 创建参考模板（使用中位数或平均值）
        if use_median:
            print("    使用中位数(median)合成模板...")
            self.reference_template_ = np.median(reference_signals, axis=1)
        else:
            print("    使用平均值(mean)合成模板...")
            self.reference_template_ = np.mean(reference_signals, axis=1)

        # 可选：对模板进行二次降噪 (例如，使用一个简单的移动平均滤波器)
        if denoise_template and len(self.reference_template_) > 0:
            print("    对模板进行二次平滑降噪...")
            # 使用一个长度为3或5的移动平均，可以有效平滑噪声且不严重影响信号形态
            window_length = min(5, len(self.reference_template_))
            if window_length % 2 == 0: # 确保窗口长度为奇数
                window_length -= 1
            if window_length > 1:
                self.reference_template_ = signal.savgol_filter(self.reference_template_, window_length, 1)

        print(
            f"    最终参考模板统计 - RMS: {np.sqrt(np.mean(self.reference_template_ ** 2)):.4f}, PTP: {np.ptp(self.reference_template_):.4f}")

        return self.reference_template_

    def _step2_multi_source_recovery(self, similarity_metric, recovery_threshold, top_sources, force_recovery,
                                     use_wavelet_filtering=False, wavelet_threshold_scale=3.0,
                                     use_lowpass_filtering=False, lowpass_cutoff=10.0, lowpass_order=4):
        """步骤二：多源ICA分离与回收"""
        if use_lowpass_filtering:
            recovery_mode = "低通滤波回收"
        elif use_wavelet_filtering:
            recovery_mode = "小波滤波回收"
        else:
            recovery_mode = "硬回收"
        print(f"\n--- 步骤 2: 多源ICA分离与回收 (选择前{top_sources}名源, 模式: {recovery_mode}) ---")

        # 创建参考模板 (现在可以控制其行为)
        self._create_reference_template(top_n_channels=3, use_median=True, denoise_template=True)
        # ICA分离
        n_channels = self.residual_.shape[1]
        try:
            self.ica_model_ = FastICA(n_components=n_channels, whiten='unit-variance',
                                      random_state=0, max_iter=1000, tol=1e-4)
            self.ica_sources_ = self.ica_model_.fit_transform(self.residual_)
            print(f"    ICA成功分离出 {n_channels} 个独立成分")
        except Exception as e:
            print(f"    ICA分离失败: {e}")
            self._handle_ica_failure()
            return

        # 计算所有源与参考模板的相似度
        scores = self._calculate_source_similarities(similarity_metric)
        self.source_scores_ = scores

        # 选择前top_sources名源
        sorted_indices = np.argsort(scores)[::-1]
        candidate_sources = sorted_indices[:top_sources]

        print(f"    所有源的相似度评分: {[round(s, 4) for s in scores]}")
        print(f"    候选源 (前{top_sources}名): {candidate_sources.tolist()}")
        print(f"    对应评分: {[round(scores[i], 4) for i in candidate_sources]}")

        # 回收决策
        if force_recovery:
            # 强制回收逻辑：确保至少回收force_recovery指定数量的源
            # 但如果有更多源超过阈值，也应该包含
            qualified_sources = [i for i in candidate_sources if scores[i] > recovery_threshold]

            # 如果超过阈值的源数量 >= force_recovery，使用所有超过阈值的源
            if len(qualified_sources) >= force_recovery:
                self.selected_sources_idx_ = qualified_sources
                print(f"    强制回收模式：{len(qualified_sources)}个源超过阈值，全部回收: {qualified_sources}")
            else:
                # 否则强制选择前force_recovery名源
                self.selected_sources_idx_ = candidate_sources[:force_recovery].tolist()
                print(f"    强制回收模式：仅{len(qualified_sources)}个源超过阈值，强制选择前{force_recovery}名源: {self.selected_sources_idx_}")

            self.recovery_applied_ = True
        else:
            # 筛选超过阈值的源
            qualified_sources = [i for i in candidate_sources if scores[i] > recovery_threshold]
            self.selected_sources_idx_ = qualified_sources
            self.recovery_applied_ = len(qualified_sources) > 0

            if self.recovery_applied_:
                print(f"    回收决策：选择{len(qualified_sources)}个超过阈值的源: {qualified_sources}")
            else:
                print(f"    回收决策：无源超过阈值 {recovery_threshold}，放弃回收")

        # 执行多源回收重构
        if use_lowpass_filtering:
            self._reconstruct_multi_source_signal_with_lowpass_filtering(lowpass_cutoff, lowpass_order)
        elif use_wavelet_filtering:
            self._reconstruct_multi_source_signal_with_wavelet_filtering(wavelet_threshold_scale)
        else:
            self._reconstruct_multi_source_signal()

    def _calculate_source_similarities(self, similarity_metric):
        """计算各源与参考模板的相似度"""
        scores = []
        n_sources = self.ica_sources_.shape[1]

        for i in range(n_sources):
            source = self.ica_sources_[:, i]
            score = 0

            if similarity_metric == 'peaks':
                try:
                    # 在参考模板中找峰
                    peaks_ref, _ = find_peaks(self.reference_template_,
                                              height=np.mean(self.reference_template_) + 1.5 * np.std(
                                                  self.reference_template_),
                                              distance=self.fs // 2)

                    if len(peaks_ref) > 1:
                        # 计算源在峰位置的能量占比
                        energy_mask = np.zeros(len(source), dtype=bool)
                        for p in peaks_ref:
                            win_start = max(0, p - int(0.05 * self.fs))
                            win_end = min(len(source), p + int(0.05 * self.fs))
                            energy_mask[win_start:win_end] = True

                        total_energy = np.sum(source ** 2)
                        peak_energy = np.sum(source[energy_mask] ** 2)
                        score = peak_energy / total_energy if total_energy > 1e-9 else 0

                except Exception as e:
                    print(f"    源 {i} 相似度计算失败: {e}")
                    score = 0

            elif similarity_metric == 'correlation':
                try:
                    if len(source) == len(self.reference_template_):
                        correlation = np.corrcoef(source, self.reference_template_)[0, 1]
                        score = abs(correlation)  # 使用绝对值，因为可能是负相关
                    else:
                        score = 0
                except Exception as e:
                    print(f"    源 {i} 相关性计算失败: {e}")
                    score = 0

            scores.append(score)

        return scores

    def _reconstruct_multi_source_signal(self):
        """重构多源回收信号"""
        if not self.recovery_applied_ or len(self.selected_sources_idx_) == 0:
            self.recovered_signal_ = np.zeros_like(self.original_signal_)
            print("    无源被选中，回收信号为零")
            return

        try:
            # 创建选中源的矩阵
            recovery_sources = np.zeros_like(self.ica_sources_)
            for idx in self.selected_sources_idx_:
                recovery_sources[:, idx] = self.ica_sources_[:, idx]

            # 通过ICA逆变换重构，每个通道保持独立性
            self.recovered_signal_ = self.ica_model_.inverse_transform(recovery_sources)

            # 计算回收统计
            total_recovered_energy = np.sum(self.recovered_signal_ ** 2)
            print(f"    成功重构 {len(self.selected_sources_idx_)} 个源的信号")
            print(f"    回收信号总能量: {total_recovered_energy:.6f}")

        except Exception as e:
            print(f"    多源重构失败: {e}")
            self.recovered_signal_ = np.zeros_like(self.original_signal_)

    def _reconstruct_multi_source_signal_with_wavelet_filtering(self, wavelet_threshold_scale=3.0):
        """使用小波滤波的多源回收重构"""
        if not self.recovery_applied_:
            self.recovered_signal_ = np.zeros_like(self.original_signal_)
            print("    无源被选中，回收信号为零")
            return

        try:
            n_sources = self.ica_sources_.shape[1]

            # 创建回收源矩阵，包含所有源（不归零）
            recovery_sources = self.ica_sources_.copy()

            # 对未选中的源进行小波软阈值滤波
            unselected_sources = [i for i in range(n_sources) if i not in self.selected_sources_idx_]

            print(f"    选中源（保持原样）: {self.selected_sources_idx_}")
            print(f"    未选中源（小波滤波）: {unselected_sources}")

            for source_idx in unselected_sources:
                source_signal = recovery_sources[:, source_idx]

                # 对该源进行小波软阈值滤波
                filtered_source = self._apply_wavelet_soft_threshold(
                    source_signal,
                    wavelet_threshold_scale=wavelet_threshold_scale
                )

                recovery_sources[:, source_idx] = filtered_source

            # 通过ICA逆变换重构
            self.recovered_signal_ = self.ica_model_.inverse_transform(recovery_sources)

            # 计算统计信息
            total_recovered_energy = np.sum(self.recovered_signal_ ** 2)
            selected_energy = np.sum([np.sum(self.ica_sources_[:, i] ** 2) for i in self.selected_sources_idx_])
            filtered_energy = np.sum([np.sum(recovery_sources[:, i] ** 2) for i in unselected_sources])

            print(f"    成功重构 {len(self.selected_sources_idx_)} 个选中源 + {len(unselected_sources)} 个滤波源")
            print(f"    选中源能量: {selected_energy:.6f}")
            print(f"    滤波源能量: {filtered_energy:.6f}")
            print(f"    回收信号总能量: {total_recovered_energy:.6f}")

        except Exception as e:
            print(f"    小波滤波重构失败: {e}")
            self.recovered_signal_ = np.zeros_like(self.original_signal_)

    def _reconstruct_multi_source_signal_with_lowpass_filtering(self, lowpass_cutoff=10.0, lowpass_order=4):
        """使用低通滤波的多源回收重构"""
        if not self.recovery_applied_:
            self.recovered_signal_ = np.zeros_like(self.original_signal_)
            print("    无源被选中，回收信号为零")
            return

        try:
            n_sources = self.ica_sources_.shape[1]

            # 创建回收源矩阵，包含所有源（不归零）
            recovery_sources = self.ica_sources_.copy()

            # 对未选中的源进行低通滤波
            unselected_sources = [i for i in range(n_sources) if i not in self.selected_sources_idx_]

            print(f"    选中源（保持原样）: {self.selected_sources_idx_}")
            print(f"    未选中源（低通滤波 {lowpass_cutoff}Hz）: {unselected_sources}")

            for source_idx in unselected_sources:
                source_signal = recovery_sources[:, source_idx]

                # 对该源进行低通滤波
                filtered_source = self._apply_lowpass_filter(
                    source_signal,
                    cutoff_freq=lowpass_cutoff,
                    filter_order=lowpass_order
                )

                recovery_sources[:, source_idx] = filtered_source

            # 通过ICA逆变换重构
            self.recovered_signal_ = self.ica_model_.inverse_transform(recovery_sources)

            # 计算统计信息
            total_recovered_energy = np.sum(self.recovered_signal_ ** 2)
            selected_energy = np.sum([np.sum(self.ica_sources_[:, i] ** 2) for i in self.selected_sources_idx_])
            filtered_energy = np.sum([np.sum(recovery_sources[:, i] ** 2) for i in unselected_sources])

            print(f"    成功重构 {len(self.selected_sources_idx_)} 个选中源 + {len(unselected_sources)} 个滤波源")
            print(f"    选中源能量: {selected_energy:.6f}")
            print(f"    滤波源能量: {filtered_energy:.6f}")
            print(f"    回收信号总能量: {total_recovered_energy:.6f}")

        except Exception as e:
            print(f"    低通滤波重构失败: {e}")
            self.recovered_signal_ = np.zeros_like(self.original_signal_)

    def _apply_wavelet_soft_threshold(self, signal, wavelet_type='db4', level=4, wavelet_threshold_scale=3.0):
        """对信号应用小波软阈值滤波"""
        try:
            # 小波分解
            coeffs = pywt.wavedec(signal, wavelet_type, level=level)

            # 估计噪声标准差（使用最高频细节系数）
            sigma = np.median(np.abs(coeffs[-1])) / 0.6745

            # 计算阈值（相对较高的阈值）
            threshold = sigma * wavelet_threshold_scale

            # 应用软阈值到细节系数
            coeffs_thresh = [coeffs[0]]  # 保留近似系数
            for i in range(1, len(coeffs)):
                thresh_coeff = pywt.threshold(coeffs[i], wavelet_threshold_scale, 'hard')
                coeffs_thresh.append(thresh_coeff)

            # 重构信号
            filtered_signal = pywt.waverec(coeffs_thresh, wavelet_type)

            # 确保长度一致
            if len(filtered_signal) != len(signal):
                filtered_signal = filtered_signal[:len(signal)]

            # 计算滤波效果
            energy_before = np.sum(signal ** 2)
            energy_after = np.sum(filtered_signal ** 2)
            energy_retention = energy_after / energy_before if energy_before > 0 else 0

            return filtered_signal

        except Exception as e:
            print(f"    小波滤波失败: {e}，返回原信号")
            return signal

    def _apply_lowpass_filter(self, signal, cutoff_freq=10.0, filter_order=4):
        """对信号应用无相移低通滤波器"""
        try:
            # 设计Butterworth低通滤波器
            nyquist = self.fs / 2
            normalized_cutoff = cutoff_freq / nyquist

            # 确保截止频率不超过奈奎斯特频率
            if normalized_cutoff >= 1.0:
                normalized_cutoff = 0.95
                print(f"    警告: 截止频率过高，调整为 {normalized_cutoff * nyquist:.1f}Hz")

            # 设计滤波器
            b, a = butter(filter_order, normalized_cutoff, btype='low')

            # 使用filtfilt进行零相位滤波，避免相移
            # 计算合适的填充长度以减少端点效应
            pad_length = 3 * max(len(b), len(a))

            # 确保填充长度不会超过信号本身长度的一半
            if pad_length > len(signal) // 2:
                pad_length = len(signal) // 2 - 1

            # 应用零相位滤波
            filtered_signal = filtfilt(b, a, signal, padlen=pad_length)

            # 计算滤波效果
            energy_before = np.sum(signal ** 2)
            energy_after = np.sum(filtered_signal ** 2)
            energy_retention = energy_after / energy_before if energy_before > 0 else 0

            return filtered_signal

        except Exception as e:
            print(f"    低通滤波失败: {e}，返回原信号")
            return signal

    def _handle_ica_failure(self):
        """处理ICA失败的情况"""
        self.ica_sources_ = np.zeros_like(self.residual_)
        self.recovered_signal_ = np.zeros_like(self.original_signal_)
        self.selected_sources_idx_ = []
        self.source_scores_ = [0.0] * self.residual_.shape[1]
        self.recovery_applied_ = False

    def _step3_finalize(self):
        """步骤三：合成最终信号"""
        print("\n--- 步骤 3: 合成最终信号 ---")
        self.final_signal_ = self.base_signal_ + self.recovered_signal_

        # 输出合成统计
        base_energy = np.sum(self.base_signal_ ** 2)
        recovery_energy = np.sum(self.recovered_signal_ ** 2)
        total_energy = np.sum(self.final_signal_ ** 2)

        print(f"    基础信号能量: {base_energy:.6f}")
        print(f"    回收信号能量: {recovery_energy:.6f}")
        print(f"    最终信号能量: {total_energy:.6f}")
        if base_energy > 0:
            print(f"    回收贡献比例: {100 * recovery_energy / base_energy:.2f}%")
        print("    处理完成！")

    def process(self, signal_nd: np.ndarray, config: dict):
        """执行完整的降噪流程"""
        self.original_signal_ = signal_nd.copy()
        self.wavelet_coeffs_ = None  # 重置状态
        processing_signal = self.original_signal_

        print(f"开始处理 {signal_nd.shape[1]} 通道信号，长度: {signal_nd.shape[0]} 样本")

        # --- 预处理步骤 (使用新的通用函数) ---
        baseline_config = config.get('baseline_filter', {'method': 'none'})
        if baseline_config.get('method', 'none') != 'none':
            # 将该方法的配置传递给函数
            processing_signal = self._preprocess_baseline_filter(processing_signal, baseline_config)

        self.preprocessed_signal_ = processing_signal.copy()

        # --- 步骤一 ---
        step1_method = config.get('step1_method', 'notch')
        if step1_method == 'notch':
            self.base_signal_ = self._step1_notch_filter(
                processing_signal,
                top_n_peaks=config.get('notch_top_n', 10),
                freq_cutoff=config.get('notch_freq_cutoff', 15.0),
                Q=config.get('notch_Q', 50.0)
            )
        elif step1_method == 'wavelet':
            self.base_signal_ = self._step1_wavelet_threshold(
                processing_signal,
                wavelet=config.get('wavelet_type', 'sym8'),
                level=config.get('wavelet_level', 8),
                threshold=config.get('wavelet_threshold', 3.0)
            )
        elif step1_method == 'notch_then_wavelet':
            print("--- 步骤 1: 串联方法 (陷波+小波) ---")
            intermediate_signal = self._step1_notch_filter(
                processing_signal,
                top_n_peaks=config.get('notch_top_n', 10),
                freq_cutoff=config.get('notch_freq_cutoff', 15.0),
                Q=config.get('notch_Q', 50.0)
            )
            self.base_signal_ = self._step1_wavelet_threshold(
                intermediate_signal,
                wavelet=config.get('wavelet_type', 'sym8'),
                level=config.get('wavelet_level', 8),
                threshold=config.get('wavelet_threshold', 3.0)
            )

        self.residual_ = processing_signal - self.base_signal_

        # --- 步骤二 (新的多源回收逻辑) ---
        recovery_threshold = config.get('recovery_threshold', 0.1)
        self._last_recovery_threshold = recovery_threshold  # 保存阈值用于可视化

        self._step2_multi_source_recovery(
            similarity_metric=config.get('similarity_metric', 'peaks'),
            recovery_threshold=recovery_threshold,
            top_sources=config.get('top_sources', 3),  # 新参数：选择前几名源
            force_recovery=config.get('force_recovery', False),
            use_wavelet_filtering=config.get('use_wavelet_filtering', False),  # 新参数：是否使用小波滤波
            wavelet_threshold_scale=config.get('wavelet_threshold_scale', 3.0),  # 新参数：小波阈值倍数
            use_lowpass_filtering=config.get('use_lowpass_filtering', False),  # 新参数：是否使用低通滤波
            lowpass_cutoff=config.get('lowpass_cutoff', 10.0),  # 新参数：低通滤波截止频率
            lowpass_order=config.get('lowpass_order', 4)  # 新参数：低通滤波阶数
        )

        # --- 步骤三 ---
        self._step3_finalize()
        return self.final_signal_

    def visualize_results(self, ch_to_plot=0):
        """增强的可视化函数"""
        if self.final_signal_ is None:
            print("请先运行 .process() 方法。")
            return

        print(f"\n--- 正在生成通道 {ch_to_plot} 的可视化图表 ---")
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(5, 3, height_ratios=[1, 1, 1, 1.2, 1.2], width_ratios=[2, 1, 1])

        fig.suptitle(f'高级降噪策略 - 通道 {ch_to_plot} 多源回收分析', fontsize=16, y=0.98)

        n_samples = self.original_signal_.shape[0]
        time_vector = np.arange(n_samples) / self.fs
        n_channels = self.original_signal_.shape[1]

        # === 主要信号处理流程 ===

        # 第一行：处理流程对比
        ax1 = fig.add_subplot(gs[0, :2])
        ax1.set_title(f'通道 {ch_to_plot}: 信号处理流程对比', fontsize=12)
        ax1.plot(time_vector, self.original_signal_[:, ch_to_plot],
                 label='原始信号', color='gray', linestyle=':', alpha=0.7, linewidth=1)
        if (self.preprocessed_signal_ is not None and
                not np.allclose(self.preprocessed_signal_, self.original_signal_)):
            ax1.plot(time_vector, self.preprocessed_signal_[:, ch_to_plot],
                     label='预处理后', color='blue', alpha=0.6, linewidth=1)
        ax1.plot(time_vector, self.base_signal_[:, ch_to_plot],
                 label='基础信号', color='cyan', alpha=0.8, linewidth=1)
        ax1.plot(time_vector, self.final_signal_[:, ch_to_plot],
                 label='最终信号', color='green', linewidth=2)
        ax1.legend()
        ax1.grid(True)
        ax1.set_ylabel('幅值')

        # 第一行右侧：原始信号频谱
        ax1_freq = fig.add_subplot(gs[0, 2])
        self._plot_spectrum(ax1_freq, self.original_signal_[:, ch_to_plot], '原始信号频谱')

        # 第二行：基础信号
        ax2 = fig.add_subplot(gs[1, :2])
        ax2.set_title(f'通道 {ch_to_plot}: 基础信号 (步骤一输出)', fontsize=12)
        ax2.plot(time_vector, self.base_signal_[:, ch_to_plot], color='blue', linewidth=1.5)
        ax2.grid(True)
        ax2.set_ylabel('幅值')

        # 第二行右侧：基础信号频谱
        ax2_freq = fig.add_subplot(gs[1, 2])
        self._plot_spectrum(ax2_freq, self.base_signal_[:, ch_to_plot], '基础信号频谱')

        # 第三行：残差与回收信号对比
        ax3 = fig.add_subplot(gs[2, :2])
        ax3.set_title(f'通道 {ch_to_plot}: 残差与回收信号对比', fontsize=12)
        ax3.plot(time_vector, self.residual_[:, ch_to_plot],
                 color='orange', alpha=0.7, linewidth=1, label='残差信号')

        recovery_signal = self.recovered_signal_[:, ch_to_plot]
        recovery_color = 'purple' if self.recovery_applied_ else 'red'
        recovery_label = f'回收信号 ({len(self.selected_sources_idx_)}源)' if self.recovery_applied_ else '回收信号 (无)'
        ax3.plot(time_vector, recovery_signal,
                 color=recovery_color, linewidth=1.5, label=recovery_label)

        # 添加统计信息
        recovery_rms = np.sqrt(np.mean(recovery_signal ** 2))
        ax3.text(0.02, 0.95, f'回收信号RMS: {recovery_rms:.4f}',
                 transform=ax3.transAxes, verticalalignment='top',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))

        ax3.legend()
        ax3.grid(True)
        ax3.set_ylabel('幅值')
        ax3.set_xlabel('时间 (s)')

        # 第三行右侧：回收信号频谱
        ax3_freq = fig.add_subplot(gs[2, 2])
        self._plot_spectrum(ax3_freq, recovery_signal, '回收信号频谱')
        ax3_freq.set_xlabel('频率 (Hz)')

        # === ICA源分离详细分析 ===

        if self.ica_sources_ is not None and n_channels > 0:
            # 第四行：参考模板与选中源对比
            ax4 = fig.add_subplot(gs[3, :])
            ax4.set_title(f'参考模板与选中源对比 (参考通道: {self.reference_channels_})', fontsize=12)

            # 显示参考模板
            ax4.plot(time_vector, self.reference_template_,
                     color='blue', linewidth=2, label='参考模板 (PTP前几名均值)', alpha=0.95)

            # 显示选中的源
            if len(self.selected_sources_idx_) > 0:
                colors = plt.cm.Set1(np.linspace(0, 1, len(self.selected_sources_idx_)))
                for i, src_idx in enumerate(self.selected_sources_idx_):
                    source = self.ica_sources_[:, src_idx]
                    score = self.source_scores_[src_idx]
                    ax4.plot(time_vector, source,
                             color=colors[i], alpha=0.6, linewidth=1.5,
                             label=f'源#{src_idx} (评分:{score:.3f})')
            else:
                ax4.text(0.5, 0.5, '无选中源', ha='center', va='center',
                         transform=ax4.transAxes, fontsize=14)

            ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax4.grid(True)
            ax4.set_ylabel('幅值')
            ax4.set_xlabel('时间 (s)')

            # 第五行：所有源的评分分析
            ax5_left = fig.add_subplot(gs[4, :2])
            ax5_right = fig.add_subplot(gs[4, 2])

            # 左侧：所有ICA源的波形（缩略版）
            ax5_left.set_title(f'所有ICA源波形概览 (共{n_channels}个)', fontsize=12)
            colors = plt.cm.tab10(np.linspace(0, 1, n_channels))

            for i in range(n_channels):
                alpha = 0.8 if i in self.selected_sources_idx_ else 0.3
                linewidth = 1.5 if i in self.selected_sources_idx_ else 0.5
                # 为清晰显示，添加偏移
                offset = i * np.std(self.ica_sources_) * 1.5
                ax5_left.plot(time_vector, self.ica_sources_[:, i] + offset,
                              color=colors[i], alpha=alpha, linewidth=linewidth)

            ax5_left.set_ylabel('幅值 (带偏移)')
            ax5_left.set_xlabel('时间 (s)')
            ax5_left.grid(True)

            # 右侧：评分柱状图
            ax5_right.set_title('源相似度评分分布', fontsize=12)
            bars = ax5_right.bar(range(n_channels), self.source_scores_,
                                 color=['red' if i in self.selected_sources_idx_ else 'lightblue'
                                        for i in range(n_channels)])

            # 添加阈值线
            if hasattr(self, '_last_recovery_threshold'):
                ax5_right.axhline(y=self._last_recovery_threshold, color='orange',
                                  linestyle='--', alpha=0.7, label='回收阈值')
                ax5_right.legend()

            ax5_right.set_xlabel('源编号')
            ax5_right.set_ylabel('相似度评分')
            ax5_right.set_xticks(range(n_channels))
            ax5_right.grid(True, axis='y')

            # 标注选中的源
            for src_idx in self.selected_sources_idx_:
                score = self.source_scores_[src_idx]
                ax5_right.text(src_idx, score + 0.01, f'{score:.3f}',
                               ha='center', va='bottom', fontweight='bold', fontsize=9)
        else:
            # 如果没有ICA结果
            ax4 = fig.add_subplot(gs[3:, :])
            ax4.text(0.5, 0.5, 'ICA源分离未执行或失败\n请检查输入信号和参数配置',
                     ha='center', va='center', fontsize=14,
                     bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.axis('off')

        plt.tight_layout()
        plt.show()

    def _plot_spectrum(self, ax, data, title):
        """辅助函数：绘制频谱图"""
        try:
            N = len(data)
            xf = np.fft.fftfreq(N, 1 / self.fs)[:N // 2]
            yf = 2.0 / N * np.abs(np.fft.fft(data)[:N // 2])
            ax.plot(xf, yf, linewidth=1)
            ax.set_title(title, fontsize=10)
            ax.set_xlim(0, 100)
            ax.grid(True)
            ax.set_ylabel('幅值', fontsize=9)
            ax.tick_params(labelsize=8)
        except Exception as e:
            ax.text(0.5, 0.5, f'频谱计算失败:\n{str(e)}',
                    ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title, fontsize=10)


def analyze_with_source_separation(
        signal_nd: np.ndarray,
        fs: float,
        method: str = 'ica',
        reject_sources: list = None,
        **kwargs
):
    """
    使用PCA、ICA、SOBI或CBSS进行盲源分离、可视化及信号重构。

    参数:
    signal_nd (np.ndarray): 输入的多通道信号，形状为 (样本数, 通道数)。
    fs (float): 采样频率 (Hz)。
    method (str): 使用的方法，'pca'、'ica'、'sobi' 或 'cbss'。默认为'ica'。
    reject_sources (list): 需要移除的源信号索引列表, e.g., [1, 3]。
                          如果为None，则只显示分离出的源信号，不进行重构。
    **kwargs: 传递给特定算法的额外参数
              - 对于SOBI: tau_max (int), n_tau (int), random_state (int)
              - 对于CBSS: n_fft (int), hop_length (int), max_iter (int), tol (float), random_state (int)
              - 对于ICA: random_state (int), max_iter (int)
              - 对于PCA: random_state (int)

    返回:
    - 如果reject_sources为None: (sources, model)
    - 如果指定了reject_sources: (reconstructed_signal, residual, sources, model)
    """
    print(f"\n--- 开始使用 {method.upper()} 进行源分离 ---")

    # 确保数据格式正确 (样本数, 特征/通道数)
    if signal_nd.shape[0] < signal_nd.shape[1]:
        signal_nd = signal_nd.T

    n_samples, n_channels = signal_nd.shape
    print(f"输入信号形状: {signal_nd.shape}")
    print(f"采样频率: {fs} Hz")

    # --- 1. 创建并训练模型 ---
    method_lower = method.lower()

    if method_lower == 'ica':
        # ICA参数
        ica_params = {
            'n_components': n_channels,
            'whiten': 'unit-variance',
            'random_state': kwargs.get('random_state', 0),
            'max_iter': kwargs.get('max_iter', 1000)
        }
        print(f"ICA 参数: {ica_params}")
        model = FastICA(**ica_params)

    elif method_lower == 'pca':
        # PCA参数
        pca_params = {
            'n_components': n_channels,
            'random_state': kwargs.get('random_state', 0)
        }
        print(f"PCA 参数: {pca_params}")
        model = PCA(**pca_params)

    elif method_lower == 'sobi':
        # SOBI参数
        sobi_params = {
            'n_components': n_channels,
            'tau_max': kwargs.get('tau_max', 10),
            'n_tau': kwargs.get('n_tau', 5),
            'random_state': kwargs.get('random_state', 0)
        }
        print(f"SOBI 参数: {sobi_params}")
        model = SOBI(**sobi_params)

    elif method_lower == 'cbss':
        # CBSS参数
        cbss_params = {
            'n_components': n_channels,
            'n_fft': kwargs.get('n_fft', 512),
            'hop_length': kwargs.get('hop_length', None),
            'max_iter': kwargs.get('max_iter', 100),
            'tol': kwargs.get('tol', 1e-6),
            'random_state': kwargs.get('random_state', 0)
        }
        if cbss_params['hop_length'] is None:
            cbss_params['hop_length'] = cbss_params['n_fft'] // 4
        print(f"CBSS 参数: {cbss_params}")
        model = ConvolutiveBSS(**cbss_params)

    else:
        raise ValueError("方法必须是 'pca'、'ica'、'sobi' 或 'cbss'")

    # 分解得到源信号
    print("正在进行源分离...")
    try:
        sources = model.fit_transform(signal_nd)
        print(f"源分离完成，得到 {sources.shape[1]} 个源信号")
    except Exception as e:
        print(f"源分离过程中出现错误: {str(e)}")
        raise

    # --- 2. 可视化分离出的源信号 ---
    print(f"已使用{method.upper()}分离出源信号，请观察下图并决定要移除哪些源。")
    n_cols = min(4, n_channels)
    n_rows = int(np.ceil(n_channels / n_cols))

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(16, n_rows * 3), sharex=True)
    if n_rows == 1 and n_cols == 1:
        axes = [axes]
    elif n_rows == 1 or n_cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()

    time_vector = np.arange(n_samples) / fs

    for i in range(n_channels):
        axes[i].plot(time_vector, sources[:, i], linewidth=0.8)
        axes[i].set_title(f'源 (Component) #{i}')
        axes[i].grid(True, alpha=0.3)
        axes[i].set_ylabel('幅值')

        # 添加源信号的统计信息
        std_val = np.std(sources[:, i])
        max_val = np.max(np.abs(sources[:, i]))
        axes[i].text(0.02, 0.95, f'std: {std_val:.3f}\nmax: {max_val:.3f}',
                     transform=axes[i].transAxes,
                     bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                     fontsize=9, verticalalignment='top')

    # 隐藏多余的子图
    for i in range(n_channels, len(axes)):
        axes[i].set_visible(False)

    # 设置x轴标签（只在最后一行）
    for i in range(max(0, len(axes) - n_cols), len(axes)):
        if axes[i].get_visible():
            axes[i].set_xlabel('时间 (s)')

    fig.suptitle(f'{method.upper()} 分离出的源信号', fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 如果没有指定要移除的源，则函数到此结束
    if reject_sources is None:
        print("未指定要移除的源 (reject_sources)，分析结束。")
        print("\n--- 源信号统计信息 ---")
        for i in range(n_channels):
            print(f"源 {i}: 标准差={np.std(sources[:, i]):.4f}, "
                  f"最大值={np.max(np.abs(sources[:, i])):.4f}")
        return sources, model

    # --- 3. 移除指定源并重构信号 ---
    print(f"正在移除源 {reject_sources} 并重构信号...")

    modified_sources = sources.copy()
    modified_sources[:, reject_sources] = 0  # 将指定源的贡献清零

    try:
        reconstructed_signal = model.inverse_transform(modified_sources)
        print("信号重构完成")
    except Exception as e:
        print(f"信号重构过程中出现错误: {str(e)}")
        raise

    # --- 4. 可视化重构结果与残差 ---
    residual = signal_nd - reconstructed_signal

    # 选择第一个通道进行对比可视化
    ch_to_plot = 0

    fig, axes = plt.subplots(4, 1, figsize=(14, 12), sharex=True)
    fig.suptitle(f'通道 {ch_to_plot} 的重构结果 (移除了源 {reject_sources})', fontsize=16)

    # 时域对比
    axes[0].plot(time_vector, signal_nd[:, ch_to_plot], label='原始信号', alpha=0.8, linewidth=1)
    axes[0].plot(time_vector, reconstructed_signal[:, ch_to_plot], label='重构信号', linewidth=1.5)
    axes[0].set_title('时域对比')
    axes[0].set_ylabel('幅值')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 残差时域
    axes[1].plot(time_vector, residual[:, ch_to_plot], label='残差 (被移除部分)', color='red', linewidth=1)
    axes[1].set_title('残差时域波形')
    axes[1].set_ylabel('幅值')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # 频谱对比
    N = n_samples
    xf = np.fft.fftfreq(N, 1 / fs)[:N // 2]
    yf_original = 2.0 / N * np.abs(np.fft.fft(signal_nd[:, ch_to_plot])[:N // 2])
    yf_reconstructed = 2.0 / N * np.abs(np.fft.fft(reconstructed_signal[:, ch_to_plot])[:N // 2])

    axes[2].plot(xf, yf_original, label='原始频谱', alpha=0.8, linewidth=1)
    axes[2].plot(xf, yf_reconstructed, label='重构频谱', linewidth=1.5)
    axes[2].set_title('频谱对比')
    axes[2].set_ylabel('幅值')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)

    # 残差频谱
    yf_residual = 2.0 / N * np.abs(np.fft.fft(residual[:, ch_to_plot])[:N // 2])
    axes[3].plot(xf, yf_residual, color='red', linewidth=1)
    axes[3].set_title('残差频谱')
    axes[3].set_xlabel('频率 (Hz)')
    axes[3].set_ylabel('幅值')
    axes[3].grid(True, alpha=0.3)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # --- 5. 打印重构质量指标 ---
    mse = np.mean((signal_nd - reconstructed_signal) ** 2)

    # 计算各通道的信噪比改善
    snr_improvements = []
    for ch in range(n_channels):
        original_power = np.var(signal_nd[:, ch])
        residual_power = np.var(residual[:, ch])
        if residual_power > 1e-12:
            snr_improvement = 10 * np.log10(original_power / residual_power)
        else:
            snr_improvement = float('inf')
        snr_improvements.append(snr_improvement)

    avg_snr_improvement = np.mean([s for s in snr_improvements if s != float('inf')])
    residual_energy_ratio = np.var(residual) / np.var(signal_nd) * 100

    print(f"\n--- 重构质量评估 ---")
    print(f"使用方法: {method.upper()}")
    print(f"移除的源: {reject_sources}")
    print(f"均方误差 (MSE): {mse:.6f}")
    print(f"平均信噪比改善 (dB): {avg_snr_improvement:.2f}")
    print(f"残差能量占比: {residual_energy_ratio:.2f}%")

    # 各通道详细信息
    print(f"\n各通道信噪比改善:")
    for ch, snr in enumerate(snr_improvements):
        if snr != float('inf'):
            print(f"  通道 {ch}: {snr:.2f} dB")
        else:
            print(f"  通道 {ch}: >100 dB (残差接近0)")

    return reconstructed_signal, residual, sources, model


def apply_flc_filter(
        signal_1d: np.ndarray,
        fs: float,
        noise_freq: float,
        n_harmonics: int = 2,
        mu: float = 0.001
):
    """
    应用FLC (Fourier Linear Combiner) 自适应滤波器消除周期性噪声。

    参数:
    signal_1d (np.ndarray): 输入的单通道信号。
    fs (float): 采样频率 (Hz)。
    noise_freq (float): 要消除的噪声基频 (Hz)。
    n_harmonics (int): 包含的谐波数量。例如，2表示处理基频和二倍谐波。
    mu (float): LMS算法的学习率，需要小心调节。
    """
    print(f"\n--- 开始FLC自适应滤波，基频: {noise_freq} Hz, 学习率 mu={mu} ---")

    n_samples = len(signal_1d)

    # 权重数量 = (基频+谐波) * (sin+cos)
    n_weights = 2 * n_harmonics
    weights = np.zeros(n_weights)

    # 创建输出数组
    cleaned_signal = np.zeros(n_samples)
    estimated_noise = np.zeros(n_samples)

    # 频率向量
    freqs = np.arange(1, n_harmonics + 1) * noise_freq

    # --- 逐样本进行自适应滤波 ---
    for k in range(n_samples):
        # 1. 创建当前时刻的参考输入向量x_k
        x_k = np.zeros(n_weights)
        t = k / fs
        for i in range(n_harmonics):
            x_k[2 * i] = np.cos(2 * np.pi * freqs[i] * t)
            x_k[2 * i + 1] = np.sin(2 * np.pi * freqs[i] * t)

        # 2. 计算滤波器输出 (噪声估计)
        y_k = np.dot(weights, x_k)

        # 3. 计算误差信号 (干净信号)
        d_k = signal_1d[k]
        e_k = d_k - y_k

        # 4. 更新权重
        weights += mu * e_k * x_k

        # 存储结果
        cleaned_signal[k] = e_k
        estimated_noise[k] = y_k

    # --- 可视化 ---
    fig, axes = plt.subplots(3, 1, figsize=(12, 10), sharex=True)
    fig.suptitle(f'FLC自适应滤波结果 (基频 {noise_freq} Hz)', fontsize=16)

    time_vector = np.arange(n_samples) / fs

    # 原始信号与干净信号对比
    axes[0].plot(time_vector*fs, signal_1d, label='原始信号', alpha=0.6)
    axes[0].plot(time_vector*fs, cleaned_signal, label='干净信号 (输出)', color='green')
    axes[0].set_title('时域对比')
    axes[0].set_ylabel('幅值')
    axes[0].legend()
    axes[0].grid(True)

    # 估计出的噪声
    axes[1].plot(time_vector*fs, estimated_noise, label='估计出的噪声', color='red')
    axes[1].set_title('自适应估计出的噪声')
    axes[1].set_ylabel('幅值')
    axes[1].legend()
    axes[1].grid(True)

    # 频谱对比
    N = n_samples
    xf = np.fft.fftfreq(N, 1 / fs)[:N // 2]
    yf_original = 2.0 / N * np.abs(np.fft.fft(signal_1d)[:N // 2])
    yf_cleaned = 2.0 / N * np.abs(np.fft.fft(cleaned_signal)[:N // 2])
    axes[2].plot(xf, yf_original, label='原始频谱', alpha=0.6)
    axes[2].plot(xf, yf_cleaned, label='干净信号频谱', color='green')
    axes[2].set_title('频谱对比')
    axes[2].set_xlabel('频率 (Hz)')
    axes[2].set_ylabel('幅值')
    axes[2].legend()
    axes[2].grid(True)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()


if __name__ == "__main__":

    # 创建分析器
    analyzer = WaveletAnalyzer(sampling_rate=1000)

    # 加载信号
    data_path = 'files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt'
    raw_signal = load_data(data_path)
    print(signal.shape)
    signal_tobe_test = raw_signal[10000:16000, 13]
    print(signal_tobe_test.shape)

    plt.plot(signal_tobe_test)
    plt.show()

    # 基础滤波测试 ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    # 陷波滤波测试
    testq = [1, 3, 5, 7, 9, 11, 13, 15]

    for q in testq:
        analyze_with_notch_filter(
            input_signal=signal_tobe_test,
            fs=1000,
            f0=40,
            Q=q,
            freq_range=(0, 60)
        )

    testlow = [16, 16.5, 17, 17.5, 18, 18.5, 19, 19.5, 20, 20.5, 21, 21.5, 22]
    for i in testlow:
        analyze_with_bandstop_filter(
            input_signal=signal_tobe_test,
            fs=1000,
            lowcut=i,
            highcut=i + 4,
            order=4,
            freq_range=(0, 60)
        )



    # 定点打击滤波 ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    apply_multi_notch_filter(
        signal_1d=signal_tobe_test,
        fs=1000,
        # notch_freqs=[17.9,19.3,20.1,22.1,25,26.8,35,40,45],
        notch_freqs=[19.3,20.1,22,25,26.8],
        Q=60
    )

    # 源分离 /迭代 SOBI/CBSS ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    signal_tobe_test = raw_signal[10000:20000, 1:9]
    reconstructsignal,residual = analyze_with_source_separation(
        signal_nd=signal_tobe_test,
        fs = 1000,
        method = 'ica',
        # tau_max=200,
        # n_tau = 100
        # reject_sources = [0,1,2,4,5,6,7] # 留下了3
        # n_fft=1024,         # 更高的频率分辨率
        # hop_length=256,     # 75%重叠
    )

    reconstructsignal2,residual2 = analyze_with_source_separation(
        signal_nd=residual,
        fs = 1000,
        method = 'ica',
        reject_sources = [0,1,2,3,5,6,7] # 留下了4
    )

    # 到这里为止已经回收了大部分信号了

    final_signal = reconstructsignal+reconstructsignal2
    final_signal.shape

    plt.plot(signal_tobe_test[:10000,0],alpha = 0.5)
    plt.plot(final_signal[:10000,0])
    plt.show()



    # FLC 方法 ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    signal_tobe_test = raw_signal[10000:30000, 13]

    apply_flc_filter(
        signal_1d = signal_tobe_test,
        fs=1000,
        noise_freq=20.2,
        n_harmonics=3,
        mu= 0.2
    )

    #

    # 小波分解 ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    # 读取信号 原始信号可视化
    signal_name = analyzer.load_signal(signal_tobe_test, "my_signal")
    analyzer.plot_signal_overview(signal_name)

    # 基础小波分解与可视化
    r1 = analyzer.wavelet_decompose(signal_name, wavelet='sym6', levels=7)
    analyzer.plot_wavelet_coefficients(signal_name)

    # 阈值处理及重建信号
    r2 = analyzer.threshold_coefficients(signal_name, threshold_method='manual', threshold_value=1,
                                         levels_to_process=[1, 2, 3])
    r3 = analyzer.reconstruct_signal(signal_name)
    analyzer.plot_reconstruction_comparison(signal_name)

    # 迭代降噪 可视化 -- 可视化需要展开更多子图，不要叠在一起，包括
    analyzer.iterative_denoising(signal_name, iterations=3, threshold_decay=0.8)
    analyzer.plot_iterative_evolution(signal_name)


    # 综合策略 +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    signal_tobe_test = raw_signal[:10000, 19:28]
    denoising_pipeline = AdvancedDenoisingStrategy(fs=1000)
    config_notch = {
        # 'baseline_filter': {
        #     'apply': True,
        #     'threshold': 2.0  # 用一个高阈值来定义和滤除基线
        # },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 14.0,
        'notch_Q': 10, # 使用稍低的Q值
        'similarity_metric': 'peaks',
        'force_recovery': True,
        'recovery_threshold': 0.1 # 只有当最佳源的R峰能量占比超过10%时才回收
    }
    final_signal_notch = denoising_pipeline.process(signal_tobe_test.copy(), config=config_notch)
    for i in range(9):
        denoising_pipeline.visualize_results(ch_to_plot=i)

    # 使用'n + wavelet'方法
    config_combo_force =  {
        'baseline_filter': {
            'method': 'lowpass',
            'cutoff_freq': 0.7,
            'order': 3
        },
        'step1_method': 'notch_then_wavelet',
        'wavelet_type': 'sym8',       # 小波参数现在是全局的
        'wavelet_level': 8,
        'wavelet_threshold': 3.0,       # 这是步骤一的阈值
        'notch_top_n': 10,
        'notch_Q': 20,
        'notch_freq_cutoff': 15.0,
        'similarity_metric': 'peaks',
        'force_recovery': True,
        'top_sources': 3,  # 选择前3名源
        'recovery_threshold': 0.1  # 只有当最佳源的R峰能量占比超过10%时才回收

    }
    final_signal_wavelet = denoising_pipeline.process(signal_tobe_test.copy(), config=config_combo_force)

    for i in range(9):
        denoising_pipeline.visualize_results(ch_to_plot=i)

    # 源分离方案需要调整
    config_combo_force =  {
        'baseline_filter': {
            'method': 'lowpass',
            'cutoff_freq': 0.7,
            'order': 3
        },
        'step1_method': 'wavelet',
        'wavelet_type': 'sym8',       # 小波参数现在是全局的
        'wavelet_level': 8,
        'wavelet_threshold': 2.0,       # 这是步骤一的阈值
        'similarity_metric': 'peaks',
        'force_recovery': True,
        'top_sources': 3,  # 选择前3名源
        'recovery_threshold': 0.1  # 只有当最佳源的R峰能量占比超过10%时才回收

    }
    final_signal_wavelet = denoising_pipeline.process(signal_tobe_test.copy(), config=config_combo_force)

    for i in range(9):
        denoising_pipeline.visualize_results(ch_to_plot=i)


    # todo  1.在广泛数据上测试验证现在的配置方案，2.选择平均；对比能效；
