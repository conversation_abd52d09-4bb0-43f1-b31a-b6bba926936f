"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: analyze_61chan.py
date: 2025/6/9
desc:
61通道数据数据分析工具，支持数据加载、频谱分析和结果可视化后保存。
"""
import os
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import pandas as pd
import glob
import argparse
import re
from vmdpy import VMD

plt.rcParams['font.family'] = 'SimHei'
def find_data_files(root_path):
    """查找所有包含'61通道.txt'的文件"""
    all_files = []
    for dirpath, dirnames, filenames in os.walk(root_path):
        for filename in filenames:
            if '61通道.txt' in filename:
                all_files.append(os.path.join(dirpath, filename))
    return all_files

def load_data(file_path):
    """加载数据文件并转换为60000*61格式"""
    try:
        # 自动检测分隔符。首先尝试默认的空白分隔符。
        try:
            # 根据用户反馈，明确使用utf-8编码
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            # 如果用空白分隔符解析失败，再尝试使用逗号作为分隔符
            print(f"使用默认分隔符读取失败，尝试使用逗号分隔符: {os.path.basename(file_path)}")
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')

        print(f"读取数据形状: {data.shape}")

        return data
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return None

def get_safe_filename(filepath):
    """生成安全的文件名，移除不合法的字符"""
    # 获取文件所在目录名和文件名
    dir_name = os.path.basename(os.path.dirname(filepath))
    file_name = os.path.basename(filepath)
    
    # 组合目录名和文件名作为标识符
    safe_name = f"{dir_name}_{file_name}"
    # 替换Windows不允许的文件名字符
    safe_name = re.sub(r'[\\/*?:"<>|]', '_', safe_name)
    return safe_name

def plot_raw_data(data, filename, save_dir):
    """绘制原始数据波形"""
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(15, 10))
    
    # 为了可视化效果，只绘制前10个通道
    num_channels_to_plot = min(5, data.shape[1])
    for i in range(num_channels_to_plot):
        plt.subplot(num_channels_to_plot, 1, i+1)
        # 为了可视化效果，只绘制前10000个采样点
        plt.plot(data[:, i])
        plt.title(f'通道 {i+1}')
        plt.ylabel('幅度')
        
    plt.tight_layout()
    # 使用安全的文件名保存
    save_path = os.path.join(save_dir, "raw_data.png")
    plt.savefig(save_path)
    plt.close()
    print(f"原始数据波形已保存至: {save_path}")

def plot_spectrum(data, filename, save_dir, fs=1000):
    """绘制频谱分析"""
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(15, 10))
    
    # 为了可视化效果，只绘制前10个通道的频谱
    num_channels_to_plot = min(10, data.shape[1])
    for i in range(num_channels_to_plot):
        plt.subplot(num_channels_to_plot, 1, i+1)
        
        # 使用Welch方法计算功率谱密度
        f, Pxx = signal.welch(data[:, i], fs, nperseg=1024)
        plt.semilogy(f, Pxx)
        plt.title(f'通道 {i+1} 频谱')
        plt.ylabel('功率谱密度 [V^2/Hz]')
        
    plt.xlabel('频率 [Hz]')
    plt.tight_layout()
    # 使用安全的文件名保存
    save_path = os.path.join(save_dir, "spectrum.png")
    plt.savefig(save_path)
    plt.close()
    print(f"频谱分析已保存至: {save_path}")

def plot_spectrogram(data, filename, save_dir, fs=1000):
    """绘制时频图"""
    os.makedirs(save_dir, exist_ok=True)
    
    # 为了可视化效果，只选择前3个通道绘制时频图
    num_channels_to_plot = min(3, data.shape[1])
    plt.figure(figsize=(15, 10))
    
    for i in range(num_channels_to_plot):
        plt.subplot(num_channels_to_plot, 1, i+1)
        f, t, Sxx = signal.spectrogram(data[:, i], fs, nperseg=256, noverlap=128)
        plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
        plt.title(f'通道 {i+1} 时频图')
        plt.ylabel('频率 [Hz]')
        
    plt.xlabel('时间 [秒]')
    plt.colorbar(label='功率/频率 [dB/Hz]')
    plt.tight_layout()
    # 使用安全的文件名保存
    save_path = os.path.join(save_dir, "spectrogram.png")
    plt.savefig(save_path)
    plt.close()
    print(f"时频图已保存至: {save_path}")

def analyze_correlation(data, filename, save_dir):
    """分析通道间的相关性"""
    os.makedirs(save_dir, exist_ok=True)
    
    # 计算相关系数矩阵
    corr_matrix = np.corrcoef(data, rowvar=False)
    
    plt.figure(figsize=(12, 10))
    plt.imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    plt.colorbar(label='相关系数')
    plt.title('61通道间的相关系数矩阵')
    plt.tight_layout()
    
    # 使用安全的文件名保存
    save_path = os.path.join(save_dir, "correlation.png")
    plt.savefig(save_path)
    plt.close()
    print(f"相关性分析已保存至: {save_path}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='61通道数据分析工具')
    parser.add_argument('--data_path', type=str, 
                        default=r"E:\个人临时文件夹\王友好\61通道\第三代测试数据\第三代测试数据",
                        help='61通道数据文件的根目录路径')
    parser.add_argument('--output_dir', type=str, default='output',
                        help='输出结果的基础保存目录')
    parser.add_argument('--sampling_rate', type=int, default=1000,
                        help='数据采样率(Hz)')
    parser.add_argument('--max_files', type=int, default=None,
                        help='最多处理的文件数量，不指定则处理全部文件')
    
    args = parser.parse_args()
    
    # 数据路径和输出基础目录
    data_root = args.data_path
    base_output_dir = args.output_dir
    fs = args.sampling_rate
    max_files = args.max_files
    
    os.makedirs(base_output_dir, exist_ok=True)
    
    # 查找所有61通道.txt文件
    data_files = find_data_files(data_root)
    print(f"找到 {len(data_files)} 个61通道数据文件")
    
    if not data_files:
        print("未找到任何61通道数据文件")
        return
    
    # 如果指定了最大文件数限制
    if max_files is not None and max_files > 0:
        data_files = data_files[:max_files]
        print(f"将处理前 {max_files} 个文件")
    
    # 创建数据摘要文件
    summary_file = os.path.join(base_output_dir, "data_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"总共找到 {len(data_files)} 个61通道数据文件\n")
        f.write(f"采样率: {fs} Hz\n\n")
        f.write(f"将处理的文件数量: {len(data_files)}\n\n")
    
    # 处理每个文件
    for i, file_path in enumerate(data_files):
        print(f"\n处理文件 {i+1}/{len(data_files)}: {file_path}")
        
        # 为每个数据文件创建独立的输出目录
        # 使用安全的目录名
        safe_name = get_safe_filename(file_path)
        file_output_dir = os.path.join(base_output_dir, safe_name)
        os.makedirs(file_output_dir, exist_ok=True)
        
        # 加载数据
        data = load_data(file_path)
        if data is None:
            continue
        
        # 获取文件名（不带扩展名）
        file_basename = os.path.basename(file_path)
        
        # 将数据分析结果写入该文件的单独摘要文件
        file_summary = os.path.join(file_output_dir, "summary.txt")
        with open(file_summary, 'w', encoding='utf-8') as f:
            f.write(f"文件: {file_path}\n")
            f.write(f"数据形状: {data.shape}\n")
            f.write(f"每个通道数值范围:\n")
            for j in range(min(10, data.shape[1])):  # 记录前10个通道的统计
                channel_data = data[:, j]
                f.write(f"  通道 {j+1}: 最小值={np.min(channel_data):.2f}, 最大值={np.max(channel_data):.2f}, 均值={np.mean(channel_data):.2f}, 标准差={np.std(channel_data):.2f}\n")
        
        # 也更新主摘要文件
        with open(summary_file, 'a', encoding='utf-8') as f:
            f.write(f"\n文件 {i+1}: {file_path}\n")
            f.write(f"  已保存至目录: {file_output_dir}\n")
            f.write(f"  数据形状: {data.shape}\n")
        
        # 数据可视化，存储到该文件的专属目录中
        plot_raw_data(data, file_basename, file_output_dir)
        plot_spectrum(data, file_basename, file_output_dir, fs=fs)
        plot_spectrogram(data, file_basename, file_output_dir, fs=fs)
        # analyze_correlation(data, file_basename, file_output_dir)
        
        print(f"完成文件 {file_path} 的分析，结果保存在 {file_output_dir}")

if __name__ == "__main__":
    main()
    print("分析完成！结果保存在指定的输出目录中。") 