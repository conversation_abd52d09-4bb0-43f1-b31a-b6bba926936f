# VMD基线降噪整合使用指南

## 概述

VMD（Variational Mode Decomposition）基线降噪类已成功整合到`wavelet_analysis.py`中的`AdvancedDenoisingStrategy`类中，作为基线去除的一个新选项。

## 主要特性

- **VMD分解**：使用变分模态分解提取信号的不同频率成分
- **智能基线提取**：自动识别最低频率模态作为基线
- **降采样优化**：通过降采样提高处理效率
- **低通滤波**：对基线进行进一步平滑处理
- **无缝集成**：与现有的小波降噪流程完全兼容

## 使用方法

### 1. 基本配置

```python
from src.wavelet_denoising.wavelet_analysis import AdvancedDenoisingStrategy

# 创建降噪策略实例
fs = 1000  # 采样频率
denoiser = AdvancedDenoisingStrategy(fs=fs)

# VMD基线降噪配置
config_vmd = {
    'baseline_filter': {
        'method': 'vmd',                # 使用VMD方法
        'original_fs': 1000,            # 原始采样频率 (Hz)
        'downsample_fs': 50,            # 降采样频率 (Hz)
        'cutoff_freq': 0.8              # 低通滤波截止频率 (Hz)
    },
    'step1_method': 'notch',            # 后续处理方法
    'notch_top_n': 5,
    'notch_freq_cutoff': 15.0,
    'notch_Q': 30.0,
    'similarity_metric': 'peaks',
    'force_recovery': False
}

# 处理信号
result = denoiser.process(signal_data, config_vmd)
```

### 2. 参数说明

#### VMD基线降噪参数

- **`method`**: 设置为 `'vmd'` 启用VMD基线降噪
- **`original_fs`**: 原始信号采样频率，默认使用创建实例时的fs
- **`downsample_fs`**: 降采样频率，默认50Hz，用于提高处理效率
- **`cutoff_freq`**: 低通滤波截止频率，默认0.8Hz，用于平滑基线

#### VMD内部参数（自动设置）

```python
vmd_params = {
    'alpha': 600,       # 带宽约束参数
    'tau': 0,           # 噪声容忍度
    'K': 6,             # 模态数量
    'DC': 0,            # 直流分量
    'init': 1,          # 初始化方法
    'tol': 1e-3         # 收敛容忍度
}
```

### 3. 与其他基线去除方法对比

```python
# 原始小波基线去除
config_wavelet = {
    'baseline_filter': {
        'method': 'wavelet',
        'wavelet_type': 'sym8',
        'wavelet_level': 6,
        'threshold': 3.0
    }
}

# 低通滤波基线去除
config_lowpass = {
    'baseline_filter': {
        'method': 'lowpass',
        'cutoff_freq': 0.5,
        'order': 4
    }
}

# VMD基线去除
config_vmd = {
    'baseline_filter': {
        'method': 'vmd',
        'original_fs': 1000,
        'downsample_fs': 50,
        'cutoff_freq': 0.8
    }
}
```

### 4. 完整使用示例

```python
import numpy as np
from src.wavelet_denoising.wavelet_analysis import AdvancedDenoisingStrategy, load_data

# 加载数据
data = load_data('your_data_file.txt')
if data is not None:
    # 选择部分通道和时间段
    signal_data = data[:10000, 9:15]  # 10秒数据，6个通道
    
    # 创建降噪器
    denoiser = AdvancedDenoisingStrategy(fs=1000)
    
    # 配置VMD基线降噪
    config = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': 1000,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': True,
        'top_sources': 3,
        'recovery_threshold': 0.1
    }
    
    # 执行处理
    result = denoiser.process(signal_data, config)
    
    # 可视化结果
    denoiser.visualize_results(ch_to_plot=0)
```

## 优势特点

1. **自适应基线提取**：VMD能够自动分解信号并识别基线成分
2. **频域精确性**：相比传统方法，VMD在频域分离更精确
3. **参数稳定性**：内置参数经过优化，适用于大多数生理信号
4. **处理效率**：通过降采样显著提高处理速度
5. **兼容性好**：与现有流程无缝集成，不影响其他功能

## 注意事项

1. **依赖库**：需要安装`vmdpy`库：`pip install vmdpy`
2. **内存使用**：VMD分解可能消耗较多内存，建议对长信号分段处理
3. **参数调优**：根据具体信号特性，可能需要调整`downsample_fs`和`cutoff_freq`
4. **处理时间**：VMD处理比简单滤波慢，但通过降采样已优化

## 测试验证

运行测试脚本验证整合效果：

```bash
python test_vmd_integration.py
```

测试结果显示VMD基线降噪在SNR改善方面表现优异，特别适合处理含有复杂基线漂移的生理信号。

## 技术细节

VMD基线降噪的处理流程：

1. **降采样**：将信号从1000Hz降采样到50Hz
2. **VMD分解**：分解为6个模态
3. **基线识别**：选择频率中心最低的模态
4. **低通滤波**：对基线模态进行0.8Hz低通滤波
5. **升采样**：将基线恢复到原始长度
6. **基线去除**：原始信号减去基线得到降噪信号

这种方法特别适合处理心电、脑电等生理信号中的基线漂移问题。
