"""
VMD基线降噪使用演示脚本
Author: Assistant
Date: 2025-07-29
Description: 演示如何在实际项目中使用整合的VMD基线降噪功能
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加src路径
sys.path.append('src')

from wavelet_denoising.wavelet_analysis import AdvancedDenoisingStrategy, load_data

def demo_vmd_baseline_removal():
    """演示VMD基线降噪的使用"""
    
    # 设置中文字体
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=== VMD基线降噪使用演示 ===")
    
    # 1. 数据准备
    print("\n1. 准备测试数据...")
    
    # 尝试加载真实数据
    real_data_path = 'files/第三代数据61channel/970静息_原始数据.txt'
    if os.path.exists(real_data_path):
        print("   使用真实数据...")
        data = load_data(real_data_path)
        if data is not None:
            data = data.T  # shape (120000, 70)
            # 选择中间的几个通道，避免参考通道
            test_signal = data[30000:40000, 20:26]  # 10秒数据，6个通道
            fs = 1000
            print(f"   真实数据加载成功，信号形状: {test_signal.shape}")
        else:
            raise ValueError("数据加载失败")
    else:
        print("   生成模拟数据...")
        # 生成更真实的模拟心电信号
        fs = 1000
        t = np.arange(0, 10, 1/fs)  # 10秒
        n_channels = 6
        
        test_signal = np.zeros((len(t), n_channels))
        for i in range(n_channels):
            # 基线漂移（呼吸等引起的低频漂移）
            baseline = 1.5 * np.sin(2 * np.pi * 0.2 * t) + 0.8 * np.sin(2 * np.pi * 0.05 * t)
            
            # 心电信号（模拟QRS复合波）
            heart_rate = 1.2  # 72 bpm
            qrs_signal = np.zeros_like(t)
            for beat in np.arange(0, 10, 1/heart_rate):
                beat_idx = int(beat * fs)
                if beat_idx < len(t) - 100:
                    # 简单的QRS波形
                    qrs_signal[beat_idx:beat_idx+50] += np.exp(-((np.arange(50)-25)/10)**2)
            
            # 肌电噪声
            emg_noise = 0.1 * np.random.randn(len(t))
            
            # 工频干扰
            powerline = 0.05 * np.sin(2 * np.pi * 50 * t)
            
            # 合成信号
            test_signal[:, i] = baseline + qrs_signal + emg_noise + powerline
            
        print(f"   模拟数据生成完成，信号形状: {test_signal.shape}")
    
    # 2. 创建不同的配置方案
    print("\n2. 创建不同的处理配置...")
    
    # 方案1：仅使用陷波滤波（无基线去除）
    config_no_baseline = {
        'baseline_filter': {'method': 'none'},
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': False
    }
    
    # 方案2：传统小波基线去除
    config_wavelet_baseline = {
        'baseline_filter': {
            'method': 'wavelet',
            'wavelet_type': 'sym8',
            'wavelet_level': 6,
            'threshold': 2.0
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': False
    }
    
    # 方案3：VMD基线去除（推荐）
    config_vmd_baseline = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': fs,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': True,
        'top_sources': 2,
        'recovery_threshold': 0.05
    }
    
    # 3. 执行不同方案的处理
    print("\n3. 执行不同处理方案...")
    
    results = {}
    configs = {
        '无基线去除': config_no_baseline,
        '小波基线去除': config_wavelet_baseline,
        'VMD基线去除': config_vmd_baseline
    }
    
    for name, config in configs.items():
        print(f"\n   处理方案: {name}")
        try:
            denoiser = AdvancedDenoisingStrategy(fs=fs)
            result = denoiser.process(test_signal.copy(), config)
            results[name] = result
            print(f"   ✓ {name} 处理成功")
        except Exception as e:
            print(f"   ✗ {name} 处理失败: {e}")
            results[name] = test_signal.copy()
    
    # 4. 结果对比可视化
    print("\n4. 生成对比可视化...")
    
    try:
        fig, axes = plt.subplots(4, 2, figsize=(16, 12))
        fig.suptitle('VMD基线降噪效果对比', fontsize=16)
        
        # 选择第一个通道进行详细分析
        channel_idx = 0
        time_axis = np.arange(len(test_signal)) / fs
        
        # 时域对比
        axes[0, 0].plot(time_axis, test_signal[:, channel_idx], 'k-', alpha=0.8, label='原始信号')
        axes[0, 0].set_title(f'原始信号 - 通道 {channel_idx}')
        axes[0, 0].set_ylabel('幅值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        colors = ['blue', 'green', 'red']
        for i, (name, result) in enumerate(results.items()):
            axes[i+1, 0].plot(time_axis, result[:, channel_idx], colors[i], alpha=0.8, label=name)
            axes[i+1, 0].set_title(f'{name} - 通道 {channel_idx}')
            axes[i+1, 0].set_ylabel('幅值')
            axes[i+1, 0].legend()
            axes[i+1, 0].grid(True, alpha=0.3)
        
        axes[3, 0].set_xlabel('时间 (s)')
        
        # 频谱对比
        def plot_spectrum(ax, signal, title, color):
            N = len(signal)
            freqs = np.fft.fftfreq(N, 1/fs)[:N//2]
            fft_signal = np.abs(np.fft.fft(signal))[:N//2]
            ax.semilogy(freqs, fft_signal, color=color, alpha=0.8)
            ax.set_title(title)
            ax.set_ylabel('幅值 (log)')
            ax.set_xlim(0, 100)
            ax.grid(True, alpha=0.3)
        
        plot_spectrum(axes[0, 1], test_signal[:, channel_idx], '原始信号频谱', 'black')
        
        for i, (name, result) in enumerate(results.items()):
            plot_spectrum(axes[i+1, 1], result[:, channel_idx], f'{name}频谱', colors[i])
        
        axes[3, 1].set_xlabel('频率 (Hz)')
        
        plt.tight_layout()
        plt.show()
        
        print("   ✓ 可视化完成")
        
    except Exception as e:
        print(f"   ✗ 可视化失败: {e}")
    
    # 5. 性能评估
    print("\n5. 性能评估...")
    
    try:
        def calculate_metrics(original, processed):
            """计算性能指标"""
            # 基线去除效果（低频能量减少）
            def get_low_freq_power(signal):
                freqs = np.fft.fftfreq(len(signal), 1/fs)
                fft_signal = np.abs(np.fft.fft(signal))**2
                low_freq_mask = (np.abs(freqs) < 2.0)  # 2Hz以下
                return np.sum(fft_signal[low_freq_mask])
            
            orig_low_power = get_low_freq_power(original)
            proc_low_power = get_low_freq_power(processed)
            baseline_reduction = (orig_low_power - proc_low_power) / orig_low_power * 100
            
            # 信号保持度（中频能量保持）
            def get_mid_freq_power(signal):
                freqs = np.fft.fftfreq(len(signal), 1/fs)
                fft_signal = np.abs(np.fft.fft(signal))**2
                mid_freq_mask = (np.abs(freqs) >= 2.0) & (np.abs(freqs) <= 40.0)
                return np.sum(fft_signal[mid_freq_mask])
            
            orig_mid_power = get_mid_freq_power(original)
            proc_mid_power = get_mid_freq_power(processed)
            signal_retention = proc_mid_power / orig_mid_power * 100
            
            return baseline_reduction, signal_retention
        
        print("   性能指标对比:")
        print("   方法名称          基线去除率(%)    信号保持率(%)")
        print("   " + "-" * 50)
        
        for name, result in results.items():
            if name != '无基线去除':  # 跳过无基线去除的方案
                baseline_red, signal_ret = calculate_metrics(test_signal[:, 0], result[:, 0])
                print(f"   {name:<15} {baseline_red:>8.1f}      {signal_ret:>8.1f}")
        
    except Exception as e:
        print(f"   性能评估失败: {e}")
    
    # 6. 使用建议
    print("\n6. 使用建议:")
    print("   ✓ VMD基线去除适合处理复杂的基线漂移")
    print("   ✓ 对于心电、脑电等生理信号效果显著")
    print("   ✓ 建议参数: downsample_fs=50, cutoff_freq=0.8")
    print("   ✓ 可根据信号特性调整参数")
    
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    demo_vmd_baseline_removal()
