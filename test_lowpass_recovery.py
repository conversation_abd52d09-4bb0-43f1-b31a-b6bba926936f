"""
测试低通滤波回收功能
Author: Assistant
Date: 2025-07-29
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加src路径
sys.path.append('src')

from wavelet_denoising.wavelet_analysis import AdvancedDenoisingStrategy

def test_lowpass_recovery():
    """测试低通滤波回收功能"""
    
    # 设置中文字体
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=== 测试低通滤波回收功能 ===")
    
    # 生成测试数据
    fs = 1000
    t = np.arange(0, 5, 1/fs)  # 5秒数据
    n_channels = 6
    
    test_signal = np.zeros((len(t), n_channels))
    for i in range(n_channels):
        # 基线漂移（低频有用信号）
        baseline = 1.0 * np.sin(2 * np.pi * 0.1 * t)
        
        # 心电信号（中频有用信号）
        ecg = np.sin(2 * np.pi * 1.2 * t) + 0.3 * np.sin(2 * np.pi * 2.4 * t)
        
        # 高频噪声（应该被滤除）
        high_freq_noise = 0.3 * np.sin(2 * np.pi * 50 * t) + 0.2 * np.sin(2 * np.pi * 100 * t)
        
        # 肌电噪声
        emg_noise = 0.1 * np.random.randn(len(t))
        
        test_signal[:, i] = baseline + ecg + high_freq_noise + emg_noise
    
    print(f"生成测试数据，形状: {test_signal.shape}")
    
    # 测试场景1：标准硬回收（原始方法）
    print("\n=== 测试场景1：标准硬回收 ===")
    
    config_hard = {
        'baseline_filter': {'method': 'none'},
        'step1_method': 'notch',
        'notch_top_n': 3,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': False,
        'use_lowpass_filtering': False
    }
    
    try:
        denoiser_hard = AdvancedDenoisingStrategy(fs=fs)
        result_hard = denoiser_hard.process(test_signal.copy(), config_hard)
        print(f"✓ 硬回收处理成功，选中源数量: {len(denoiser_hard.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_hard.selected_sources_idx_}")
    except Exception as e:
        print(f"✗ 硬回收处理失败: {e}")
        result_hard = test_signal.copy()
    
    # 测试场景2：小波滤波回收
    print("\n=== 测试场景2：小波滤波回收 ===")
    
    config_wavelet = {
        'baseline_filter': {'method': 'none'},
        'step1_method': 'notch',
        'notch_top_n': 3,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': True,
        'wavelet_threshold_scale': 2.0,
        'use_lowpass_filtering': False
    }
    
    try:
        denoiser_wavelet = AdvancedDenoisingStrategy(fs=fs)
        result_wavelet = denoiser_wavelet.process(test_signal.copy(), config_wavelet)
        print(f"✓ 小波滤波回收处理成功，选中源数量: {len(denoiser_wavelet.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_wavelet.selected_sources_idx_}")
    except Exception as e:
        print(f"✗ 小波滤波回收处理失败: {e}")
        result_wavelet = test_signal.copy()
    
    # 测试场景3：低通滤波回收（新功能）
    print("\n=== 测试场景3：低通滤波回收（新功能） ===")
    
    config_lowpass = {
        'baseline_filter': {'method': 'none'},
        'step1_method': 'notch',
        'notch_top_n': 3,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': False,
        'use_lowpass_filtering': True,
        'lowpass_cutoff': 15.0,  # 15Hz低通滤波
        'lowpass_order': 4
    }
    
    try:
        denoiser_lowpass = AdvancedDenoisingStrategy(fs=fs)
        result_lowpass = denoiser_lowpass.process(test_signal.copy(), config_lowpass)
        print(f"✓ 低通滤波回收处理成功，选中源数量: {len(denoiser_lowpass.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_lowpass.selected_sources_idx_}")
    except Exception as e:
        print(f"✗ 低通滤波回收处理失败: {e}")
        result_lowpass = test_signal.copy()
    
    # 测试场景4：结合VMD基线去除和低通滤波回收
    print("\n=== 测试场景4：VMD基线去除 + 低通滤波回收 ===")
    
    config_vmd_lowpass = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': fs,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 3,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': False,
        'use_lowpass_filtering': True,
        'lowpass_cutoff': 12.0,  # 12Hz低通滤波
        'lowpass_order': 4
    }
    
    try:
        denoiser_vmd_lowpass = AdvancedDenoisingStrategy(fs=fs)
        result_vmd_lowpass = denoiser_vmd_lowpass.process(test_signal.copy(), config_vmd_lowpass)
        print(f"✓ VMD+低通滤波回收处理成功，选中源数量: {len(denoiser_vmd_lowpass.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_vmd_lowpass.selected_sources_idx_}")
    except Exception as e:
        print(f"✗ VMD+低通滤波回收处理失败: {e}")
        result_vmd_lowpass = test_signal.copy()
    
    # 结果对比可视化
    print("\n=== 生成对比可视化 ===")
    
    try:
        fig, axes = plt.subplots(5, 2, figsize=(16, 15))
        fig.suptitle('低通滤波回收功能测试结果', fontsize=16)
        
        # 选择第一个通道进行可视化
        channel_idx = 0
        time_axis = np.arange(len(test_signal)) / fs
        
        results = {
            '原始信号': test_signal,
            '硬回收': result_hard,
            '小波滤波回收': result_wavelet,
            '低通滤波回收': result_lowpass,
            'VMD+低通滤波回收': result_vmd_lowpass
        }
        
        colors = ['black', 'blue', 'green', 'red', 'purple']
        
        # 时域对比
        for i, (name, result) in enumerate(results.items()):
            axes[i, 0].plot(time_axis, result[:, channel_idx], colors[i], alpha=0.8, label=name)
            axes[i, 0].set_title(f'{name} - 通道 {channel_idx}')
            axes[i, 0].set_ylabel('幅值')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)
        
        axes[4, 0].set_xlabel('时间 (s)')
        
        # 频谱对比
        def plot_spectrum(ax, signal, title, color):
            N = len(signal)
            freqs = np.fft.fftfreq(N, 1/fs)[:N//2]
            fft_signal = np.abs(np.fft.fft(signal))[:N//2]
            ax.semilogy(freqs, fft_signal, color=color, alpha=0.8)
            ax.set_title(title)
            ax.set_ylabel('幅值 (log)')
            ax.set_xlim(0, 150)
            ax.grid(True, alpha=0.3)
        
        for i, (name, result) in enumerate(results.items()):
            plot_spectrum(axes[i, 1], result[:, channel_idx], f'{name}频谱', colors[i])
        
        axes[4, 1].set_xlabel('频率 (Hz)')
        
        plt.tight_layout()
        plt.show()
        
        print("✓ 可视化完成")
        
    except Exception as e:
        print(f"✗ 可视化失败: {e}")
    
    # 性能评估
    print("\n=== 性能评估 ===")
    
    try:
        def calculate_metrics(original, processed):
            """计算性能指标"""
            # 高频噪声抑制（50Hz以上能量减少）
            def get_high_freq_power(signal):
                freqs = np.fft.fftfreq(len(signal), 1/fs)
                fft_signal = np.abs(np.fft.fft(signal))**2
                high_freq_mask = (np.abs(freqs) > 50.0)
                return np.sum(fft_signal[high_freq_mask])
            
            orig_high_power = get_high_freq_power(original)
            proc_high_power = get_high_freq_power(processed)
            noise_reduction = (orig_high_power - proc_high_power) / orig_high_power * 100 if orig_high_power > 0 else 0
            
            # 低频信号保持（2-20Hz能量保持）
            def get_useful_freq_power(signal):
                freqs = np.fft.fftfreq(len(signal), 1/fs)
                fft_signal = np.abs(np.fft.fft(signal))**2
                useful_freq_mask = (np.abs(freqs) >= 2.0) & (np.abs(freqs) <= 20.0)
                return np.sum(fft_signal[useful_freq_mask])
            
            orig_useful_power = get_useful_freq_power(original)
            proc_useful_power = get_useful_freq_power(processed)
            signal_retention = proc_useful_power / orig_useful_power * 100 if orig_useful_power > 0 else 0
            
            return noise_reduction, signal_retention
        
        print("   方法名称              高频噪声抑制(%)    有用信号保持(%)")
        print("   " + "-" * 60)
        
        for name, result in results.items():
            if name != '原始信号':
                noise_red, signal_ret = calculate_metrics(test_signal[:, 0], result[:, 0])
                print(f"   {name:<20} {noise_red:>8.1f}         {signal_ret:>8.1f}")
        
    except Exception as e:
        print(f"   性能评估失败: {e}")
    
    print("\n=== 测试总结 ===")
    print("✓ 低通滤波回收功能已成功实现")
    print("✓ 新功能特点：")
    print("  - 对未选中的源进行低通滤波而不是归零")
    print("  - 保留低频有用信息，去除高频噪声")
    print("  - 使用零相位滤波避免相移")
    print("  - 可与VMD基线去除等功能结合使用")
    print("✓ 使用方法：在config中设置 'use_lowpass_filtering': True")

if __name__ == "__main__":
    test_lowpass_recovery()
