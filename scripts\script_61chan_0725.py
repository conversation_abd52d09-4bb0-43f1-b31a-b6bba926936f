"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_61chan_0725
date: 2025/7/25 16:17
desc: 
"""

import numpy as np
import matplotlib.pyplot as plt
from vmdpy import VMD  # 核心 VMD库
import os
import glob
import pandas as pd
from scipy import signal
from scipy.signal import filtfilt, butter, resample
from scipy.signal import decimate

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题


def load_data(file_path):
    try:
        try:
            # 首先尝试使用默认分隔符
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            # 如果失败，尝试使用逗号分隔符
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
        return data
    except Exception as e:
        return None


def simple_plot_data(data,fs , channels, time_range, title,baseline = None):
    plt.figure(figsize=(20, 6))
    # 如果data 仅1维度，则追加
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    for channel in channels:
        plt.plot(data[time_range[0]*fs:time_range[1]*fs, channel], label=f'Channel {channel}')
    if baseline is not None:
        plt.plot(baseline[time_range[0]*fs:time_range[1]*fs], label=f'baseline {channel}',color = 'r')

    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.show()


def compare_plot_data(original_data, denoised_data, fs, channels, time_range, titles, baseline=None):
    plt.figure(figsize=(20, 12))  # 增大图形尺寸以适应两个子图

    # 第一个子图：原始信号
    plt.subplot(2, 1, 1)  # 2行1列，第1个子图
    if len(original_data.shape) == 1:
        original_data = original_data.reshape(-1, 1)
    for channel in channels:
        plt.plot(original_data[time_range[0] * fs:time_range[1] * fs, channel], label=f'Channel {channel}')
    if baseline is not None:
        plt.plot(baseline[time_range[0] * fs:time_range[1] * fs], label=f'baseline {channel}', color='r')
    plt.title(titles[0])
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.legend()

    # 第二个子图：去噪后的信号
    plt.subplot(2, 1, 2)  # 2行1列，第2个子图
    if len(denoised_data.shape) == 1:
        denoised_data = denoised_data.reshape(-1, 1)
    for channel in channels:
        plt.plot(denoised_data[time_range[0] * fs:time_range[1] * fs, channel], label=f'Channel {channel}')
    plt.title(titles[1])
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.legend()

    plt.tight_layout()  # 自动调整子图间距
    plt.show()

class VMDBaselineDenoiser:
    """VMD基线降噪类"""

    def __init__(self, original_fs=1000, downsample_fs=50, cutoff_freq=0.8):
        """
        初始化VMD基线降噪器

        Args:
            original_fs: 原始采样频率 (Hz)
            downsample_fs: 降采样频率 (Hz)
            cutoff_freq: 低通滤波截止频率 (Hz)
        """
        self.original_fs = original_fs
        self.downsample_fs = downsample_fs
        self.cutoff_freq = cutoff_freq
        self.vmd_params = {
            'alpha': 600,
            'tau': 0,
            'K': 6,
            'DC': 0,
            'init': 1,
            'tol': 1e-3
        }


    def downsample_signal(self, signal):
        """
        使用抗混叠滤波器进行降采样，以避免信号混叠。
        """
        downsample_ratio = self.original_fs // self.downsample_fs

        # 使用scipy.signal.decimate函数，它会自动应用抗混叠滤波器后再进行抽取
        # zero_phase=True 确保了滤波是无相移的，这对于保持信号形态至关重要
        downsampled = decimate(signal, downsample_ratio, ftype='iir', zero_phase=True)

        return downsampled

    def upsample_signal(self, signal, target_length):
        """使用线性插值升采样信号到原始长度，以避免端点震荡"""
        # 创建原始（降采样后）和目标（原始长度）的时间点
        original_points = np.linspace(0, 1, len(signal))
        target_points = np.linspace(0, 1, target_length)

        # 使用线性插值
        upsampled = np.interp(target_points, original_points, signal)
        return upsampled

    def apply_lowpass_filter(self,signal, fs, cutoff):
        """应用无相移低通滤波器，并优化端点效应"""
        # 设计Butterworth低通滤波器
        nyquist = fs / 2
        normalized_cutoff = cutoff / nyquist

        # 确保截止频率不超过奈奎斯特频率
        if normalized_cutoff >= 1.0:
            normalized_cutoff = 0.95

        # 滤波器阶数为4
        order = 4
        b, a = butter(order, normalized_cutoff, btype='low')

        # 使用filtfilt进行零相位滤波，并添加 padlen 参数
        # 一个好的经验法则是 3 * (order), 这里我们用更安全的 3 * (len(b)-1)
        pad_length = 3 * (len(b) - 1)

        # 确保填充长度不会超过信号本身长度的一半
        if pad_length > len(signal) // 2:
            pad_length = len(signal) // 2 - 1

        filtered_signal = filtfilt(b, a, signal, padlen=pad_length)
        return filtered_signal

    def find_lowest_frequency_mode(self, vmd_modes, vmd_omega):
        """找到频率中心最低的VMD分量"""
        # 获取最后一次迭代的频率
        final_omega = vmd_omega[-1, :]

        # 找到最低频率分量的索引
        lowest_freq_idx = np.argmin(final_omega)

        return vmd_modes[lowest_freq_idx, :], lowest_freq_idx, final_omega[lowest_freq_idx]

    def denoise_single_channel(self, signal):
        """
        对单个通道进行VMD基线降噪

        Args:
            signal: 输入信号 (1D numpy array)

        Returns:
            denoised_signal: 降噪后的信号
            baseline: 提取的基线
            vmd_info: VMD分解信息
        """
        original_length = len(signal)

        # 步骤1: 降采样
        downsampled_signal = self.downsample_signal(signal)

        # 步骤2: VMD分解
        vmd_modes, vmd_u_hat, vmd_omega = VMD(
            downsampled_signal,
            self.vmd_params['alpha'],
            self.vmd_params['tau'],
            self.vmd_params['K'],
            self.vmd_params['DC'],
            self.vmd_params['init'],
            self.vmd_params['tol']
        )

        # 步骤3: 找到最低频率分量
        lowest_freq_mode, mode_idx, center_freq = self.find_lowest_frequency_mode(vmd_modes, vmd_omega)

        # 步骤4: 对最低频率分量进行低通滤波
        filtered_baseline = self.apply_lowpass_filter(
            lowest_freq_mode,
            self.downsample_fs,
            self.cutoff_freq
        )

        # 步骤5: 升采样基线到原始长度
        upsampled_baseline = self.upsample_signal(filtered_baseline, original_length)

        # 步骤6: 原始信号减去基线得到降噪信号
        denoised_signal = signal - upsampled_baseline
        denoised_signal = denoised_signal - np.mean(denoised_signal)
        upsampled_baseline =  signal - denoised_signal


        # 返回结果和相关信息
        vmd_info = {
            'vmd_modes': vmd_modes,
            'vmd_omega': vmd_omega,
            'lowest_freq_mode_idx': mode_idx,
            'center_frequency': center_freq,
            'downsampled_signal': downsampled_signal,
            'filtered_baseline': filtered_baseline
        }

        return denoised_signal, upsampled_baseline, vmd_info

    def visualize_denoising_result(self, signal, denoised_signal, baseline, vmd_info,
                                 channel_idx, time_range=(50, 75)):
        """
        可视化降噪结果

        Args:
            signal: 原始信号
            denoised_signal: 降噪后信号
            baseline: 提取的基线
            vmd_info: VMD分解信息
            channel_idx: 通道索引
            time_range: 显示的时间范围 (秒)
        """
        # 计算时间轴
        time_axis = np.arange(len(signal)) / self.original_fs
        start_idx = int(time_range[0] * self.original_fs)
        end_idx = int(time_range[1] * self.original_fs)

        # 创建子图
        fig, axes = plt.subplots(3, 2, figsize=(20, 15))
        fig.suptitle(f'通道 {channel_idx} VMD基线降噪结果', fontsize=16)

        # 1. 原始信号 vs 降噪信号对比
        axes[0, 0].plot(time_axis[start_idx:end_idx], signal[start_idx:end_idx],
                       'b-', label='原始信号', alpha=0.7)
        axes[0, 0].plot(time_axis[start_idx:end_idx], denoised_signal[start_idx:end_idx],
                       'r-', label='降噪信号', alpha=0.8)
        axes[0, 0].set_title('原始信号 vs 降噪信号')
        axes[0, 0].set_xlabel('时间 (秒)')
        axes[0, 0].set_ylabel('幅度')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 提取的基线
        axes[0, 1].plot(time_axis[start_idx:end_idx], baseline[start_idx:end_idx],
                       'g-', label='提取的基线', linewidth=2)
        axes[0, 1].set_title('提取的基线')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('幅度')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. VMD分解的各个模态 (降采样后的)
        vmd_modes = vmd_info['vmd_modes']
        downsampled_time = np.arange(len(vmd_modes[0])) / self.downsample_fs

        for i in range(vmd_modes.shape[0]):
            axes[1, 0].plot(downsampled_time, vmd_modes[i, :],
                           label=f'模态 {i+1} (中心频率: {vmd_info["vmd_omega"][-1, i]:.3f})')
        axes[1, 0].set_title('VMD分解模态 (降采样后)')
        axes[1, 0].set_xlabel('时间 (秒)')
        axes[1, 0].set_ylabel('幅度')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 最低频率模态及其滤波结果
        lowest_idx = vmd_info['lowest_freq_mode_idx']
        axes[1, 1].plot(downsampled_time, vmd_modes[lowest_idx, :],
                       'b-', label=f'最低频模态 {lowest_idx+1}', alpha=0.7)
        axes[1, 1].plot(downsampled_time, vmd_info['filtered_baseline'],
                       'r-', label='滤波后基线', linewidth=2)
        axes[1, 1].set_title(f'最低频模态滤波 (中心频率: {vmd_info["center_frequency"]:.3f})')
        axes[1, 1].set_xlabel('时间 (秒)')
        axes[1, 1].set_ylabel('幅度')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 5. 频谱对比
        from scipy.fft import fft, fftfreq

        # 计算原始信号和降噪信号的频谱
        n = len(signal)
        freqs = fftfreq(n, 1/self.original_fs)[:n//2]

        original_fft = np.abs(fft(signal))[:n//2]
        denoised_fft = np.abs(fft(denoised_signal))[:n//2]

        axes[2, 0].semilogy(freqs, original_fft, 'b-', label='原始信号频谱', alpha=0.7)
        axes[2, 0].semilogy(freqs, denoised_fft, 'r-', label='降噪信号频谱', alpha=0.8)
        axes[2, 0].set_xlim(0, 50)  # 只显示0-50Hz
        axes[2, 0].set_title('频谱对比 (0-50Hz)')
        axes[2, 0].set_xlabel('频率 (Hz)')
        axes[2, 0].set_ylabel('幅度')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)

        # 6. 降噪效果统计
        # 计算信噪比改善
        noise_before = np.std(signal[60*self.original_fs:])  # 60秒后的纯噪声段
        noise_after = np.std(denoised_signal[60*self.original_fs:])
        snr_improvement = 20 * np.log10(noise_before / noise_after) if noise_after > 0 else 0

        # 计算基线去除效果
        baseline_power = np.mean(baseline**2)
        signal_power = np.mean(signal**2)
        baseline_ratio = baseline_power / signal_power * 100

        stats_text = f"""降噪统计信息:
    
            信噪比改善: {snr_improvement:.2f} dB
            基线功率占比: {baseline_ratio:.2f}%
            VMD模态数: {vmd_modes.shape[0]}
            最低频模态: {lowest_idx+1} (中心频率: {vmd_info["center_frequency"]:.3f})
            降采样比: {self.original_fs//self.downsample_fs}:1
            滤波截止频率: {self.cutoff_freq} Hz"""

        axes[2, 1].text(0.1, 0.5, stats_text, transform=axes[2, 1].transAxes,
                       fontsize=12, verticalalignment='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        axes[2, 1].set_title('降噪统计信息')
        axes[2, 1].axis('off')

        plt.tight_layout()
        plt.show()

        return snr_improvement, baseline_ratio


def process_and_save_data(input_path, output_path, denoiser):
    # 加载原始数据
    original_data = load_data(input_path).T  # shape (120000, 70)

    if original_data is None:
        print("无法加载数据文件")
        return

    # 创建处理后的数据副本
    processed_data = original_data.copy()

    # 处理每个通道 (9-69)
    for channel_idx in range(9, 70):
        print(f"\n处理通道 {channel_idx}...")

        # 提取单通道信号
        signal = original_data[:, channel_idx]

        # 进行VMD基线降噪
        denoised_signal, baseline, vmd_info = denoiser.denoise_single_channel(signal)

        # 将处理后的信号放回对应通道
        processed_data[:, channel_idx] = denoised_signal

    # 保存处理后的数据（保持原始格式）
    try:
        # 尝试以默认格式保存
        np.savetxt(output_path, processed_data.T, fmt='%.14f', encoding='utf-8')
    except:
        # 如果失败，尝试使用逗号分隔符
        np.savetxt(output_path, processed_data.T, fmt='%.14f', delimiter=',', encoding='utf-8')

    print(f"\n处理完成！结果已保存到: {output_path}")

    return processed_data


def process_and_save_data2(input_path, output_path, denoiser):
    # 加载原始数据
    original_data = load_data(input_path)  # shape (120000, 70)

    if original_data is None:
        print("无法加载数据文件")
        return

    # 创建处理后的数据副本
    processed_data = original_data.copy()

    # 处理每个通道 (9-69)
    for channel_idx in range(56):
        print(f"\n处理通道 {channel_idx}...")

        # 提取单通道信号
        signal = original_data[:, channel_idx]

        # 进行VMD基线降噪
        denoised_signal, baseline, vmd_info = denoiser.denoise_single_channel(signal)

        # 将处理后的信号放回对应通道
        processed_data[:, channel_idx] = denoised_signal

    # 保存处理后的数据（保持原始格式）
    try:
        # 尝试以默认格式保存
        np.savetxt(output_path, processed_data, fmt='%.6f', encoding='utf-8')
    except:
        # 如果失败，尝试使用逗号分隔符
        np.savetxt(output_path, processed_data, fmt='%.6f', delimiter=',', encoding='utf-8')

    print(f"\n处理完成！结果已保存到: {output_path}")

    return processed_data



if __name__ == "__main__":
    file_path = 'files/第三代数据61channel/970静息_原始数据.txt'
    data = load_data(file_path).T  # shape (120000，70)
    fs = 1000


    # 前8个是参考 中61通道 9-69是待降噪信号
    # 所以实际要处理的信号就是 通道9-69
    # 原始数据中，120000个采样点是120秒信号，其中60秒以后是纯噪声

    # 创建VMD基线降噪器
    denoiser = VMDBaselineDenoiser(original_fs=fs, downsample_fs=50, cutoff_freq=0.7)

    # 选择几个通道进行降噪演示 +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    demo_channels = [9, 15, 25, 35]  # 选择4个代表性通道

    print("开始VMD基线降噪处理...")#range(9,70)

    for channel_idx in range(9,70):

        print(f"\n处理通道 {channel_idx}...")

        # 提取单通道信号
        signal = data[:, channel_idx]

        # 进行VMD基线降噪
        denoised_signal, baseline, vmd_info = denoiser.denoise_single_channel(signal)

        # 可视化结果
        snr_improvement, baseline_ratio = denoiser.visualize_denoising_result(
            signal, denoised_signal, baseline, vmd_info, channel_idx
        )

        # simple_plot_data(signal,1000,[0],(45,75),'origin',baseline)
        # simple_plot_data(denoised_signal,1000,[0],(45,75),'baseline_denoised')

        compare_plot_data(signal, denoised_signal, 1000, [0], (45, 75),
                          ['Original Signal', 'Denoised Signal'],
                          baseline)
        print(f"通道 {channel_idx} 处理完成:")
        # print(f"  - 信噪比改善: {snr_improvement:.2f} dB")
        # print(f"  - 基线功率占比: {baseline_ratio:.2f}%")

    print("\nVMD基线降噪处理完成!")


    # 批量处理并保存下来 +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

    # 使用示例
    input_file = 'files/第三代数据61channel/971静息_原始数据.txt'
    output_file = 'files/第三代数据61channel/971静息_处理数据_VMD去基线.txt'
    processed_data = process_and_save_data(input_file, output_file, denoiser)
    processed_data.shape # (120000，70)
    simple_plot_data(processed_data,1000,[20],(45,75),'origin')



    # 0728
    input_file = 'files/0728基线降噪任务/去噪后数据/开机/前60_970仅滤波_sobi.txt'
    output_file = 'files/0728基线降噪任务/去噪后数据/开机/前60_970仅滤波_sobi_vmd.txt'
    processed_data = process_and_save_data2(input_file, output_file, denoiser)

    data = load_data(input_file)  # shape (120000，70)
    fs = 1000