"""
测试新的小波滤波回收逻辑
Author: Assistant
Date: 2025-07-29
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加src路径
sys.path.append('src')

from wavelet_denoising.wavelet_analysis import AdvancedDenoisingStrategy

def test_wavelet_filtering_recovery():
    """测试小波滤波回收逻辑"""
    
    # 设置中文字体
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=== 测试小波滤波回收逻辑 ===")
    
    # 生成测试数据
    fs = 1000
    t = np.arange(0, 8, 1/fs)  # 8秒数据
    n_channels = 6
    
    test_signal = np.zeros((len(t), n_channels))
    for i in range(n_channels):
        # 基线漂移
        baseline = 1.5 * np.sin(2 * np.pi * 0.1 * t) + 0.8 * np.sin(2 * np.pi * 0.05 * t)
        # 心电信号（模拟QRS复合波）
        heart_rate = 1.2  # 72 bpm
        qrs_signal = np.zeros_like(t)
        for beat in np.arange(0, 8, 1/heart_rate):
            beat_idx = int(beat * fs)
            if beat_idx < len(t) - 100:
                qrs_signal[beat_idx:beat_idx+50] += np.exp(-((np.arange(50)-25)/10)**2)
        # 肌电噪声
        emg_noise = 0.15 * np.random.randn(len(t))
        # 工频干扰
        powerline = 0.08 * np.sin(2 * np.pi * 50 * t)
        
        test_signal[:, i] = baseline + qrs_signal + emg_noise + powerline
    
    print(f"生成测试数据，形状: {test_signal.shape}")
    
    # 测试场景1：传统硬回收模式
    print("\n=== 场景1：传统硬回收模式 ===")
    
    config_hard = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': fs,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': False  # 传统硬回收
    }
    
    try:
        denoiser_hard = AdvancedDenoisingStrategy(fs=fs)
        result_hard = denoiser_hard.process(test_signal.copy(), config_hard)
        print(f"✓ 硬回收模式处理成功")
        print(f"  选中源数量: {len(denoiser_hard.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_hard.selected_sources_idx_}")
        
        # 计算回收信号能量
        recovery_energy_hard = np.sum(denoiser_hard.recovered_signal_ ** 2)
        print(f"  回收信号能量: {recovery_energy_hard:.6f}")
        
    except Exception as e:
        print(f"✗ 硬回收模式处理失败: {e}")
        result_hard = test_signal.copy()
        denoiser_hard = None
    
    # 测试场景2：小波滤波回收模式
    print("\n=== 场景2：小波滤波回收模式 ===")
    
    config_wavelet = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': fs,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': True,  # 使用小波滤波回收
        'wavelet_threshold_scale': 3.0  # 小波阈值倍数
    }
    
    try:
        denoiser_wavelet = AdvancedDenoisingStrategy(fs=fs)
        result_wavelet = denoiser_wavelet.process(test_signal.copy(), config_wavelet)
        print(f"✓ 小波滤波回收模式处理成功")
        print(f"  选中源数量: {len(denoiser_wavelet.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_wavelet.selected_sources_idx_}")
        
        # 计算回收信号能量
        recovery_energy_wavelet = np.sum(denoiser_wavelet.recovered_signal_ ** 2)
        print(f"  回收信号能量: {recovery_energy_wavelet:.6f}")
        
    except Exception as e:
        print(f"✗ 小波滤波回收模式处理失败: {e}")
        result_wavelet = test_signal.copy()
        denoiser_wavelet = None
    
    # 测试场景3：不同阈值倍数的小波滤波
    print("\n=== 场景3：高阈值小波滤波回收模式 ===")
    
    config_high_threshold = {
        'baseline_filter': {
            'method': 'vmd',
            'original_fs': fs,
            'downsample_fs': 50,
            'cutoff_freq': 0.8
        },
        'step1_method': 'notch',
        'notch_top_n': 5,
        'notch_freq_cutoff': 15.0,
        'notch_Q': 30.0,
        'similarity_metric': 'peaks',
        'force_recovery': 2,
        'recovery_threshold': 0.1,
        'top_sources': 4,
        'use_wavelet_filtering': True,
        'wavelet_threshold_scale': 5.0  # 更高的阈值倍数
    }
    
    try:
        denoiser_high = AdvancedDenoisingStrategy(fs=fs)
        result_high = denoiser_high.process(test_signal.copy(), config_high_threshold)
        print(f"✓ 高阈值小波滤波回收模式处理成功")
        print(f"  选中源数量: {len(denoiser_high.selected_sources_idx_)}")
        print(f"  选中的源: {denoiser_high.selected_sources_idx_}")
        
        # 计算回收信号能量
        recovery_energy_high = np.sum(denoiser_high.recovered_signal_ ** 2)
        print(f"  回收信号能量: {recovery_energy_high:.6f}")
        
    except Exception as e:
        print(f"✗ 高阈值小波滤波回收模式处理失败: {e}")
        result_high = test_signal.copy()
        denoiser_high = None
    
    # 对比可视化
    print("\n=== 生成对比可视化 ===")
    
    try:
        fig, axes = plt.subplots(4, 2, figsize=(16, 14))
        fig.suptitle('小波滤波回收逻辑对比测试', fontsize=16)
        
        # 选择第一个通道进行可视化
        channel_idx = 0
        time_axis = np.arange(len(test_signal)) / fs
        
        # 时域对比
        axes[0, 0].plot(time_axis, test_signal[:, channel_idx], 'k-', alpha=0.8, label='原始信号')
        axes[0, 0].set_title(f'原始信号 - 通道 {channel_idx}')
        axes[0, 0].set_ylabel('幅值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        if denoiser_hard is not None:
            axes[1, 0].plot(time_axis, result_hard[:, channel_idx], 'b-', alpha=0.8, label='硬回收模式')
            axes[1, 0].set_title(f'硬回收模式结果 - 通道 {channel_idx}')
            axes[1, 0].set_ylabel('幅值')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        if denoiser_wavelet is not None:
            axes[2, 0].plot(time_axis, result_wavelet[:, channel_idx], 'g-', alpha=0.8, label='小波滤波回收')
            axes[2, 0].set_title(f'小波滤波回收结果 - 通道 {channel_idx}')
            axes[2, 0].set_ylabel('幅值')
            axes[2, 0].legend()
            axes[2, 0].grid(True, alpha=0.3)
        
        if denoiser_high is not None:
            axes[3, 0].plot(time_axis, result_high[:, channel_idx], 'r-', alpha=0.8, label='高阈值小波滤波')
            axes[3, 0].set_title(f'高阈值小波滤波结果 - 通道 {channel_idx}')
            axes[3, 0].set_xlabel('时间 (s)')
            axes[3, 0].set_ylabel('幅值')
            axes[3, 0].legend()
            axes[3, 0].grid(True, alpha=0.3)
        
        # 频谱对比
        def plot_spectrum(ax, signal, title, color):
            N = len(signal)
            freqs = np.fft.fftfreq(N, 1/fs)[:N//2]
            fft_signal = np.abs(np.fft.fft(signal))[:N//2]
            ax.semilogy(freqs, fft_signal, color=color, alpha=0.8)
            ax.set_title(title)
            ax.set_ylabel('幅值 (log)')
            ax.set_xlim(0, 100)
            ax.grid(True, alpha=0.3)
        
        plot_spectrum(axes[0, 1], test_signal[:, channel_idx], '原始信号频谱', 'black')
        
        if denoiser_hard is not None:
            plot_spectrum(axes[1, 1], result_hard[:, channel_idx], '硬回收模式频谱', 'blue')
        
        if denoiser_wavelet is not None:
            plot_spectrum(axes[2, 1], result_wavelet[:, channel_idx], '小波滤波回收频谱', 'green')
        
        if denoiser_high is not None:
            plot_spectrum(axes[3, 1], result_high[:, channel_idx], '高阈值小波滤波频谱', 'red')
            axes[3, 1].set_xlabel('频率 (Hz)')
        
        plt.tight_layout()
        plt.show()
        
        print("✓ 可视化完成")
        
    except Exception as e:
        print(f"✗ 可视化失败: {e}")
    
    # 性能对比
    print("\n=== 性能对比分析 ===")
    
    try:
        def calculate_signal_quality(original, processed):
            # 计算信噪比
            signal_power = np.var(processed)
            noise_power = np.var(original - processed)
            snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else float('inf')
            
            # 计算相关系数
            correlation = np.corrcoef(original, processed)[0, 1]
            
            return snr, correlation
        
        print("方法                    SNR (dB)    相关系数    回收能量")
        print("-" * 60)
        
        if denoiser_hard is not None:
            snr_hard, corr_hard = calculate_signal_quality(test_signal[:, 0], result_hard[:, 0])
            recovery_energy_hard = np.sum(denoiser_hard.recovered_signal_ ** 2)
            print(f"硬回收模式              {snr_hard:>8.2f}    {corr_hard:>8.4f}    {recovery_energy_hard:>10.2e}")
        
        if denoiser_wavelet is not None:
            snr_wavelet, corr_wavelet = calculate_signal_quality(test_signal[:, 0], result_wavelet[:, 0])
            recovery_energy_wavelet = np.sum(denoiser_wavelet.recovered_signal_ ** 2)
            print(f"小波滤波回收            {snr_wavelet:>8.2f}    {corr_wavelet:>8.4f}    {recovery_energy_wavelet:>10.2e}")
        
        if denoiser_high is not None:
            snr_high, corr_high = calculate_signal_quality(test_signal[:, 0], result_high[:, 0])
            recovery_energy_high = np.sum(denoiser_high.recovered_signal_ ** 2)
            print(f"高阈值小波滤波          {snr_high:>8.2f}    {corr_high:>8.4f}    {recovery_energy_high:>10.2e}")
        
    except Exception as e:
        print(f"性能对比计算失败: {e}")
    
    print("\n=== 测试总结 ===")
    print("✓ 新的小波滤波回收逻辑已成功实现")
    print("✓ 对未选中的源进行小波软阈值滤波，而不是直接归零")
    print("✓ 可以通过wavelet_threshold_scale参数调节滤波强度")
    print("✓ 小波滤波回收模式能够保留更多有用信息")
    print("\n使用方法：")
    print("config = {")
    print("    'use_wavelet_filtering': True,  # 启用小波滤波回收")
    print("    'wavelet_threshold_scale': 3.0,  # 阈值倍数（越大滤波越强）")
    print("    # ... 其他参数")
    print("}")

if __name__ == "__main__":
    test_wavelet_filtering_recovery()
